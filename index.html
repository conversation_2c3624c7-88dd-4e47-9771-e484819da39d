<!doctype html>
<html lang="en-US">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <title>Next Level Growth Partners - AI Lead Conversion Experts</title>
    <meta name='robots' content='max-image-preview:large' />
    <style>
        img:is([sizes="auto" i], [sizes^="auto," i]) {
            contain-intrinsic-size: 3000px 1500px
        }
    </style>
    <link rel="alternate" type="application/rss+xml" title="Brandmode &raquo; Feed"
        href="https://brandmode.1onestrong.com/feed/" />
    <link rel="alternate" type="application/rss+xml" title="Brandmode &raquo; Comments Feed"
        href="https://brandmode.1onestrong.com/comments/feed/" />
    <script>
        window._wpemojiSettings = { "baseUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/72x72\/", "ext": ".png", "svgUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/svg\/", "svgExt": ".svg", "source": { "concatemoji": "https:\/\/brandmode.1onestrong.com\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.1" } };
        /*! This file is auto-generated */
        !function (i, n) { var o, s, e; function c(e) { try { var t = { supportTests: e, timestamp: (new Date).valueOf() }; sessionStorage.setItem(o, JSON.stringify(t)) } catch (e) { } } function p(e, t, n) { e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(t, 0, 0); var t = new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data), r = (e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(n, 0, 0), new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data)); return t.every(function (e, t) { return e === r[t] }) } function u(e, t, n) { switch (t) { case "flag": return n(e, "\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f", "\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f") ? !1 : !n(e, "\ud83c\uddfa\ud83c\uddf3", "\ud83c\uddfa\u200b\ud83c\uddf3") && !n(e, "\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f", "\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f"); case "emoji": return !n(e, "\ud83d\udc26\u200d\ud83d\udd25", "\ud83d\udc26\u200b\ud83d\udd25") }return !1 } function f(e, t, n) { var r = "undefined" != typeof WorkerGlobalScope && self instanceof WorkerGlobalScope ? new OffscreenCanvas(300, 150) : i.createElement("canvas"), a = r.getContext("2d", { willReadFrequently: !0 }), o = (a.textBaseline = "top", a.font = "600 32px Arial", {}); return e.forEach(function (e) { o[e] = t(a, e, n) }), o } function t(e) { var t = i.createElement("script"); t.src = e, t.defer = !0, i.head.appendChild(t) } "undefined" != typeof Promise && (o = "wpEmojiSettingsSupports", s = ["flag", "emoji"], n.supports = { everything: !0, everythingExceptFlag: !0 }, e = new Promise(function (e) { i.addEventListener("DOMContentLoaded", e, { once: !0 }) }), new Promise(function (t) { var n = function () { try { var e = JSON.parse(sessionStorage.getItem(o)); if ("object" == typeof e && "number" == typeof e.timestamp && (new Date).valueOf() < e.timestamp + 604800 && "object" == typeof e.supportTests) return e.supportTests } catch (e) { } return null }(); if (!n) { if ("undefined" != typeof Worker && "undefined" != typeof OffscreenCanvas && "undefined" != typeof URL && URL.createObjectURL && "undefined" != typeof Blob) try { var e = "postMessage(" + f.toString() + "(" + [JSON.stringify(s), u.toString(), p.toString()].join(",") + "));", r = new Blob([e], { type: "text/javascript" }), a = new Worker(URL.createObjectURL(r), { name: "wpTestEmojiSupports" }); return void (a.onmessage = function (e) { c(n = e.data), a.terminate(), t(n) }) } catch (e) { } c(n = f(s, u, p)) } t(n) }).then(function (e) { for (var t in e) n.supports[t] = e[t], n.supports.everything = n.supports.everything && n.supports[t], "flag" !== t && (n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && n.supports[t]); n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && !n.supports.flag, n.DOMReady = !1, n.readyCallback = function () { n.DOMReady = !0 } }).then(function () { return e }).then(function () { var e; n.supports.everything || (n.readyCallback(), (e = n.source || {}).concatemoji ? t(e.concatemoji) : e.wpemoji && e.twemoji && (t(e.twemoji), t(e.wpemoji))) })) }((window, document), window._wpemojiSettings);
    </script>
    <script src="newsletter.js" defer></script>
    <style id='wp-emoji-styles-inline-css'>
        img.wp-smiley,
        img.emoji {
            display: inline !important;
            border: none !important;
            box-shadow: none !important;
            height: 1em !important;
            width: 1em !important;
            margin: 0 0.07em !important;
            vertical-align: -0.1em !important;
            background: none !important;
            padding: 0 !important;
        }
    </style>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Webhook Configuration -->
    <script src="./webhook-config.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Logo Styles */
        .site-logo-link {
            display: inline-block;
            text-decoration: none;
            margin: 0;
            padding: 0;
        }

        .site-logo-img {
            max-height: 50px;
            width: auto;
            display: block;
            margin: 0;
            padding: 0;
            border: none;
            background: none;
        }

        /* Prevent blue sections and margin issues */
        .elementor-widget-theme-site-logo .elementor-widget-container {
            margin: 0 !important;
            padding: 0 !important;
            background: none !important;
        }

        .elementor-element-f0c84a9 {
            margin: 0 !important;
            padding: 0 !important;
            background: none !important;
        }

        /* Custom scrollbars */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #0a122e;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: #1e40af;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #1e3a8a;
        }

        /* Custom animations for modal */
        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        .modal-enter {
            animation: modalFadeIn 0.3s ease-out;
        }

        .overlay-enter {
            animation: fadeIn 0.2s ease-out;
        }
    </style>
    <style>
        /* * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
    
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
                overflow-x: hidden;
            }
     */
        .container {
            display: flex;
            gap: 60px;
            max-width: 1400px;
            width: 100%;
            align-items: center;
            justify-content: center;
        }

        .info-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 50px;
            max-width: 550px;
            box-shadow: 0 32px 64px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .info-card h1 {
            font-size: 32px;
            color: #1a1a1a;
            margin-bottom: 24px;
            line-height: 1.2;
            font-weight: 700;
            letter-spacing: -0.5px;
        }

        .info-card p {
            color: #4a5568;
            margin-bottom: 32px;
            line-height: 1.7;
            font-size: 16px;
        }

        .highlight {
            color: #2563eb;
            font-weight: 700;
            background: linear-gradient(135deg, #2563eb, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .cta-buttons {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2563eb, #3b82f6);
            color: white;
            border: none;
            padding: 18px 36px;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 24px rgba(37, 99, 235, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(37, 99, 235, 0.4);
        }

        .btn-secondary {
            background: transparent;
            color: #2563eb;
            border: 2px solid #2563eb;
            padding: 16px 36px;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #2563eb;
            color: white;
            transform: translateY(-1px);
        }

        /* iPhone Frame */
        .mobile-frame {
            width: 375px;
            height: 812px;
            background: #1a1a1a;
            border-radius: 40px;
            padding: 4px;
            box-shadow: 0 40px 80px rgba(0, 0, 0, 0.4);
            position: relative;
            overflow: hidden;
        }

        .mobile-frame::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 30px;
            background: #1a1a1a;
            border-radius: 0 0 20px 20px;
            z-index: 10;
        }

        .mobile-screen {
            width: 100%;
            height: 100%;
            background: #000;
            border-radius: 36px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* Status Bar */
        .status-bar {
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
            padding: 12px 24px 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 15px;
            font-weight: 600;
            color: white;
            position: relative;
            z-index: 5;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        /* Chat Header */
        .chat-header {
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(20px);
            color: white;
            padding: 16px 20px;
            display: flex;
            align-items: center;
            gap: 16px;
            border-bottom: 0.5px solid rgba(255, 255, 255, 0.1);
        }

        .back-btn {
            font-size: 20px;
            cursor: pointer;
            color: #007AFF;
            font-weight: 500;
        }

        .contact-info {
            flex: 1;
            text-align: center;
        }

        .contact-name {
            font-weight: 600;
            font-size: 17px;
            color: white;
        }

        .contact-status {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 2px;
        }

        .call-btn {
            color: #007AFF;
            font-size: 20px;
            cursor: pointer;
        }

        /* Messages Area */
        .chat-messages {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            background: #000;
            scroll-behavior: smooth;
        }

        .chat-messages::-webkit-scrollbar {
            display: none;
        }

        .message {
            margin-bottom: 12px;
            display: flex;
            align-items: flex-end;
            gap: 8px;
            animation: messageSlide 0.3s ease-out;
        }

        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.sent {
            flex-direction: row-reverse;
        }

        .message-bubble {
            max-width: 75%;
            padding: 12px 16px;
            border-radius: 20px;
            font-size: 16px;
            line-height: 1.4;
            position: relative;
            word-wrap: break-word;
        }

        .message.received .message-bubble {
            background: rgba(58, 58, 60, 1);
            color: white;
            border-bottom-left-radius: 6px;
        }

        .message.sent .message-bubble {
            background: linear-gradient(135deg, #007AFF, #0051D5);
            color: white;
            border-bottom-right-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
        }

        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #007AFF, #0051D5);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        /* Chat Input */
        .chat-input {
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(20px);
            padding: 12px 16px 34px;
            display: flex;
            align-items: center;
            gap: 12px;
            border-top: 0.5px solid rgba(255, 255, 255, 0.1);
        }

        .input-container {
            flex: 1;
            background: rgba(58, 58, 60, 1);
            border-radius: 20px;
            /* padding: 8px 16px; */
            display: flex;
            align-items: center;
            gap: 8px;
            min-height: 36px;
        }

        .input-field {
            flex: 1;
            border: none;
            background: transparent;
            color: white;
            font-size: 16px;
            outline: none;
            resize: none;
            max-height: 100px;
            font-family: inherit;
        }

        .input-field::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .send-btn {
            background: linear-gradient(135deg, #007AFF, #0051D5);
            color: white;
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
        }

        .send-btn:hover {
            transform: scale(1.05);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Typing Indicator */
        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background: rgba(58, 58, 60, 1);
            border-radius: 20px;
            border-bottom-left-radius: 6px;
            max-width: 75%;
            margin-bottom: 12px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {

            0%,
            60%,
            100% {
                transform: scale(1);
                opacity: 0.5;
            }

            30% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .container {
                flex-direction: column;
                gap: 40px;
                padding: 20px;
            }

            .info-card {
                order: 2;
                padding: 40px;
                max-width: 100%;
            }

            .mobile-frame {
                order: 1;
                width: 350px;
                height: 750px;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                gap: 30px;
            }

            .info-card {
                padding: 30px;
                border-radius: 20px;
            }

            .info-card h1 {
                font-size: 28px;
            }

            .mobile-frame {
                width: 320px;
                height: 680px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0;
            }

            .info-card {
                margin: 0 10px;
                padding: 24px;
            }

            .mobile-frame {
                width: 300px;
                height: 640px;
            }
        }

        /* Navigation Bar */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
            padding: 12px 20px;
            display: none;
            align-items: center;
            justify-content: space-between;
            z-index: 1000;
            border-bottom: 0.5px solid rgba(255, 255, 255, 0.1);
        }

        .navbar.show {
            display: flex;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .nav-brand {
            color: white;
            font-weight: 600;
            font-size: 18px;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .fullscreen-toggle {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .fullscreen-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .menu-btn {
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
        }

        /* Fullscreen mode */
        .fullscreen-mode {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 999;
            background: #000;
        }

        .fullscreen-mode .mobile-frame {
            width: 100vw;
            height: 100vh;
            border-radius: 0;
            padding: 0;
            box-shadow: none;
        }

        .fullscreen-mode .mobile-frame::before {
            display: none;
        }

        .fullscreen-mode .mobile-screen {
            border-radius: 0;
            height: 100vh;
        }

        .fullscreen-mode .status-bar {
            padding-top: 50px;
        }

        /* Mobile responsive adjustments */
        @media (max-width: 768px) {
            .navbar {
                display: flex;
            }

            body {
                padding-top: 60px;
            }
        }

        @media (max-width: 414px) {
            .container {
                padding: 20px 10px;
            }

            .info-card {
                order: 2;
                margin-top: 20px;
            }

            .mobile-frame {
                order: 1;
                width: 300px;
                height: 600px;
            }
        }

        /* Process Flow Section Styles */
        .process-flow-section {
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            padding: 80px 0;
            position: relative;
            overflow: hidden;
        }

        .process-flow-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        /* Animated particles background */
        .process-flow-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
                radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.4), transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.3), transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: float 20s infinite linear;
            opacity: 0.6;
        }

        @keyframes float {
            0% {
                transform: translateY(0px) translateX(0px);
            }

            33% {
                transform: translateY(-10px) translateX(10px);
            }

            66% {
                transform: translateY(5px) translateX(-5px);
            }

            100% {
                transform: translateY(0px) translateX(0px);
            }
        }

        .process-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 1;
        }

        .process-header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInUp 0.8s ease-out;
        }

        .process-emoji {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        .process-title {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            font-family: 'Inter Tight', sans-serif;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .process-subtitle {
            font-size: 1.25rem;
            color: rgba(255, 255, 255, 0.9);
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .process-timeline {
            position: relative;
            max-width: 800px;
            margin: 0 auto 60px;
        }

        .process-step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 40px;
            opacity: 0;
            transform: translateX(-50px) scale(0.9);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
        }

        .process-step.animate {
            opacity: 1;
            transform: translateX(0) scale(1);
        }

        .process-step:nth-child(even) {
            flex-direction: row-reverse;
            transform: translateX(50px) scale(0.9);
        }

        .process-step:nth-child(even).animate {
            transform: translateX(0) scale(1);
        }

        /* Timeline progress indicator */
        .timeline-progress {
            position: absolute;
            left: 50%;
            top: 0;
            width: 4px;
            height: 0%;
            background: linear-gradient(to bottom, #4facfe, #00f2fe);
            transform: translateX(-50%);
            transition: height 1s ease-out;
            border-radius: 2px;
            box-shadow: 0 0 10px rgba(79, 172, 254, 0.5);
        }

        .timeline-progress.active {
            height: 100%;
        }

        .step-number {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            margin: 0 30px;
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
            position: relative;
            z-index: 2;
            flex-shrink: 0;
            animation: pulse-glow 2s infinite;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            border-radius: 50%;
            z-index: -1;
            opacity: 0;
            animation: ripple 3s infinite;
        }

        @keyframes pulse-glow {

            0%,
            100% {
                box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4), 0 0 20px rgba(0, 123, 255, 0.3);
            }

            50% {
                box-shadow: 0 12px 35px rgba(0, 123, 255, 0.6), 0 0 30px rgba(0, 123, 255, 0.6);
            }
        }

        @keyframes ripple {
            0% {
                transform: scale(1);
                opacity: 0.8;
            }

            50% {
                transform: scale(1.2);
                opacity: 0.4;
            }

            100% {
                transform: scale(1.4);
                opacity: 0;
            }
        }

        /* Enhanced Process Flow Styles */
        .process-flow-container {
            position: relative;
        }

        .timeline-progress-vertical {
            position: absolute;
            left: 50%;
            top: 0;
            width: 4px;
            height: 0%;
            background: linear-gradient(to bottom, #4facfe, #00f2fe);
            transform: translateX(-50%);
            transition: height 2s ease-out;
            border-radius: 2px;
            box-shadow: 0 0 15px rgba(79, 172, 254, 0.6);
            z-index: 1;
        }

        .timeline-progress-vertical.active {
            height: 100%;
        }

        .process-step-item {
            position: relative;
            transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .process-step-item::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            transform: translate(-50%, -50%) scale(0);
            transition: all 0.8s ease;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .process-step-item.animate::before {
            transform: translate(-50%, -50%) scale(1);
            animation: pulse-step 2s infinite;
        }

        .process-step-item[data-step="1"]::before {
            content: "1";
        }

        .process-step-item[data-step="2"]::before {
            content: "2";
        }

        .process-step-item[data-step="3"]::before {
            content: "3";
        }

        .process-step-item[data-step="4"]::before {
            content: "4";
        }

        @keyframes pulse-step {

            0%,
            100% {
                box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
            }

            50% {
                box-shadow: 0 0 40px rgba(102, 126, 234, 0.8);
            }
        }

        .process-step-item.animate {
            transform: translateY(0) scale(1);
        }

        .process-step-item:hover {
            transform: translateY(-10px) scale(1.05);
        }

        .process-step-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(79, 172, 254, 0.1), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 10px;
        }

        .process-step-item.animate::after {
            opacity: 1;
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }

            100% {
                transform: translateX(100%);
            }
        }

        .step-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            max-width: 400px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .step-content:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .step-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .step-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
            font-family: 'Inter Tight', sans-serif;
        }

        .step-description {
            color: #666;
            line-height: 1.6;
            font-size: 1rem;
        }

        .step-connector {
            position: absolute;
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
            width: 2px;
            height: 80px;
            /* background: linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0.3)); */
            z-index: 1;
        }

        .process-step:last-child .step-connector {
            display: none;
        }

        .process-cta {
            text-align: center;
            animation: fadeInUp 0.8s ease-out 0.5s both;
        }

        .process-btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 18px 40px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
            margin-bottom: 15px;
        }

        .process-btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0, 123, 255, 0.5);
        }

        .process-cta-note {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            margin: 0;
        }

        /* Process Flow Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {

            0%,
            20%,
            50%,
            80%,
            100% {
                transform: translateY(0);
            }

            40% {
                transform: translateY(-10px);
            }

            60% {
                transform: translateY(-5px);
            }
        }

        /* Process Flow Responsive Styles */
        @media (max-width: 768px) {
            .process-title {
                font-size: 2.5rem;
            }

            .process-subtitle {
                font-size: 1.1rem;
                padding: 0 10px;
            }

            .process-step {
                flex-direction: column !important;
                align-items: center;
                text-align: center;
                transform: translateY(30px) !important;
            }

            .process-step.animate {
                transform: translateY(0) !important;
            }

            .step-number {
                margin: 0 0 20px 0;
            }

            .step-content {
                max-width: 100%;
                margin: 0 10px;
            }

            .step-connector {
                left: 30px;
                top: 80px;
                height: 60px;
            }

            .process-container {
                padding: 0 15px;
            }
        }

        @media (max-width: 480px) {
            .process-flow-section {
                padding: 60px 0;
            }

            .process-title {
                font-size: 2rem;
            }

            .process-emoji {
                font-size: 3rem;
            }

            .step-content {
                padding: 20px;
            }

            .step-title {
                font-size: 1.25rem;
            }

            .step-description {
                font-size: 0.9rem;
            }

            .process-btn-primary {
                padding: 15px 30px;
                font-size: 1rem;
            }
        }
    </style>
    <style id='global-styles-inline-css'>
        :root {
            --wp--preset--aspect-ratio--square: 1;
            --wp--preset--aspect-ratio--4-3: 4/3;
            --wp--preset--aspect-ratio--3-4: 3/4;
            --wp--preset--aspect-ratio--3-2: 3/2;
            --wp--preset--aspect-ratio--2-3: 2/3;
            --wp--preset--aspect-ratio--16-9: 16/9;
            --wp--preset--aspect-ratio--9-16: 9/16;
            --wp--preset--color--black: #000000;
            --wp--preset--color--cyan-bluish-gray: #abb8c3;
            --wp--preset--color--white: #ffffff;
            --wp--preset--color--pale-pink: #f78da7;
            --wp--preset--color--vivid-red: #cf2e2e;
            --wp--preset--color--luminous-vivid-orange: #ff6900;
            --wp--preset--color--luminous-vivid-amber: #fcb900;
            --wp--preset--color--light-green-cyan: #7bdcb5;
            --wp--preset--color--vivid-green-cyan: #00d084;
            --wp--preset--color--pale-cyan-blue: #8ed1fc;
            --wp--preset--color--vivid-cyan-blue: #0693e3;
            --wp--preset--color--vivid-purple: #9b51e0;
            --wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, rgb(155, 81, 224) 100%);
            --wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg, rgb(122, 220, 180) 0%, rgb(0, 208, 130) 100%);
            --wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg, rgba(252, 185, 0, 1) 0%, rgba(255, 105, 0, 1) 100%);
            --wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg, rgba(255, 105, 0, 1) 0%, rgb(207, 46, 46) 100%);
            --wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg, rgb(238, 238, 238) 0%, rgb(169, 184, 195) 100%);
            --wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg, rgb(74, 234, 220) 0%, rgb(151, 120, 209) 20%, rgb(207, 42, 186) 40%, rgb(238, 44, 130) 60%, rgb(251, 105, 98) 80%, rgb(254, 248, 76) 100%);
            --wp--preset--gradient--blush-light-purple: linear-gradient(135deg, rgb(255, 206, 236) 0%, rgb(152, 150, 240) 100%);
            --wp--preset--gradient--blush-bordeaux: linear-gradient(135deg, rgb(254, 205, 165) 0%, rgb(254, 45, 45) 50%, rgb(107, 0, 62) 100%);
            --wp--preset--gradient--luminous-dusk: linear-gradient(135deg, rgb(255, 203, 112) 0%, rgb(199, 81, 192) 50%, rgb(65, 88, 208) 100%);
            --wp--preset--gradient--pale-ocean: linear-gradient(135deg, rgb(255, 245, 203) 0%, rgb(182, 227, 212) 50%, rgb(51, 167, 181) 100%);
            --wp--preset--gradient--electric-grass: linear-gradient(135deg, rgb(202, 248, 128) 0%, rgb(113, 206, 126) 100%);
            --wp--preset--gradient--midnight: linear-gradient(135deg, rgb(2, 3, 129) 0%, rgb(40, 116, 252) 100%);
            --wp--preset--font-size--small: 13px;
            --wp--preset--font-size--medium: 20px;
            --wp--preset--font-size--large: 36px;
            --wp--preset--font-size--x-large: 42px;
            --wp--preset--spacing--20: 0.44rem;
            --wp--preset--spacing--30: 0.67rem;
            --wp--preset--spacing--40: 1rem;
            --wp--preset--spacing--50: 1.5rem;
            --wp--preset--spacing--60: 2.25rem;
            --wp--preset--spacing--70: 3.38rem;
            --wp--preset--spacing--80: 5.06rem;
            --wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
            --wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);
            --wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);
        }

        :root {
            --wp--style--global--content-size: 800px;
            --wp--style--global--wide-size: 1200px;
        }

        :where(body) {
            margin: 0;
        }

        .wp-site-blocks>.alignleft {
            float: left;
            margin-right: 2em;
        }

        .wp-site-blocks>.alignright {
            float: right;
            margin-left: 2em;
        }

        .wp-site-blocks>.aligncenter {
            justify-content: center;
            margin-left: auto;
            margin-right: auto;
        }

        :where(.wp-site-blocks)>* {
            margin-block-start: 24px;
            margin-block-end: 0;
        }

        :where(.wp-site-blocks)> :first-child {
            margin-block-start: 0;
        }

        :where(.wp-site-blocks)> :last-child {
            margin-block-end: 0;
        }

        :root {
            --wp--style--block-gap: 24px;
        }

        :root :where(.is-layout-flow)> :first-child {
            margin-block-start: 0;
        }

        :root :where(.is-layout-flow)> :last-child {
            margin-block-end: 0;
        }

        :root :where(.is-layout-flow)>* {
            margin-block-start: 24px;
            margin-block-end: 0;
        }

        :root :where(.is-layout-constrained)> :first-child {
            margin-block-start: 0;
        }

        :root :where(.is-layout-constrained)> :last-child {
            margin-block-end: 0;
        }

        :root :where(.is-layout-constrained)>* {
            margin-block-start: 24px;
            margin-block-end: 0;
        }

        :root :where(.is-layout-flex) {
            gap: 24px;
        }

        :root :where(.is-layout-grid) {
            gap: 24px;
        }

        .is-layout-flow>.alignleft {
            float: left;
            margin-inline-start: 0;
            margin-inline-end: 2em;
        }

        .is-layout-flow>.alignright {
            float: right;
            margin-inline-start: 2em;
            margin-inline-end: 0;
        }

        .is-layout-flow>.aligncenter {
            margin-left: auto !important;
            margin-right: auto !important;
        }

        .is-layout-constrained>.alignleft {
            float: left;
            margin-inline-start: 0;
            margin-inline-end: 2em;
        }

        .is-layout-constrained>.alignright {
            float: right;
            margin-inline-start: 2em;
            margin-inline-end: 0;
        }

        .is-layout-constrained>.aligncenter {
            margin-left: auto !important;
            margin-right: auto !important;
        }

        .is-layout-constrained> :where(:not(.alignleft):not(.alignright):not(.alignfull)) {
            max-width: var(--wp--style--global--content-size);
            margin-left: auto !important;
            margin-right: auto !important;
        }

        .is-layout-constrained>.alignwide {
            max-width: var(--wp--style--global--wide-size);
        }

        body .is-layout-flex {
            display: flex;
        }

        .is-layout-flex {
            flex-wrap: wrap;
            align-items: center;
        }

        .is-layout-flex> :is(*, div) {
            margin: 0;
        }

        body .is-layout-grid {
            display: grid;
        }

        .is-layout-grid> :is(*, div) {
            margin: 0;
        }

        body {
            padding-top: 0px;
            padding-right: 0px;
            padding-bottom: 0px;
            padding-left: 0px;
        }

        a:where(:not(.wp-element-button)) {
            text-decoration: underline;
        }

        :root :where(.wp-element-button, .wp-block-button__link) {
            background-color: #32373c;
            border-width: 0;
            color: #fff;
            font-family: inherit;
            font-size: inherit;
            line-height: inherit;
            padding: calc(0.667em + 2px) calc(1.333em + 2px);
            text-decoration: none;
        }

        .has-black-color {
            color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-color {
            color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-color {
            color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-color {
            color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-color {
            color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-color {
            color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-color {
            color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-color {
            color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-color {
            color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-color {
            color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-color {
            color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-color {
            color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-background-color {
            background-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-background-color {
            background-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-background-color {
            background-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-background-color {
            background-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-background-color {
            background-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-background-color {
            background-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-background-color {
            background-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-background-color {
            background-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-background-color {
            background-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-background-color {
            background-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-border-color {
            border-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-border-color {
            border-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-border-color {
            border-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-border-color {
            border-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-border-color {
            border-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-border-color {
            border-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-border-color {
            border-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-border-color {
            border-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-border-color {
            border-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-border-color {
            border-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-vivid-cyan-blue-to-vivid-purple-gradient-background {
            background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;
        }

        .has-light-green-cyan-to-vivid-green-cyan-gradient-background {
            background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;
        }

        .has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-orange-to-vivid-red-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;
        }

        .has-very-light-gray-to-cyan-bluish-gray-gradient-background {
            background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;
        }

        .has-cool-to-warm-spectrum-gradient-background {
            background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;
        }

        .has-blush-light-purple-gradient-background {
            background: var(--wp--preset--gradient--blush-light-purple) !important;
        }

        .has-blush-bordeaux-gradient-background {
            background: var(--wp--preset--gradient--blush-bordeaux) !important;
        }

        .has-luminous-dusk-gradient-background {
            background: var(--wp--preset--gradient--luminous-dusk) !important;
        }

        .has-pale-ocean-gradient-background {
            background: var(--wp--preset--gradient--pale-ocean) !important;
        }

        .has-electric-grass-gradient-background {
            background: var(--wp--preset--gradient--electric-grass) !important;
        }

        .has-midnight-gradient-background {
            background: var(--wp--preset--gradient--midnight) !important;
        }

        .has-small-font-size {
            font-size: var(--wp--preset--font-size--small) !important;
        }

        .has-medium-font-size {
            font-size: var(--wp--preset--font-size--medium) !important;
        }

        .has-large-font-size {
            font-size: var(--wp--preset--font-size--large) !important;
        }

        .has-x-large-font-size {
            font-size: var(--wp--preset--font-size--x-large) !important;
        }

        :root :where(.wp-block-pullquote) {
            font-size: 1.5em;
            line-height: 1.6;
        }
    </style>
    <link rel='stylesheet' id='template-kit-export-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/template-kit-export/assets/public/template-kit-export-public.css?ver=1.0.23'
        media='all' />
    <link rel='stylesheet' id='hello-elementor-css'
        href='https://brandmode.1onestrong.com/wp-content/themes/hello-elementor/style.min.css?ver=3.3.0' media='all' />
    <link rel='stylesheet' id='hello-elementor-theme-style-css'
        href='https://brandmode.1onestrong.com/wp-content/themes/hello-elementor/theme.min.css?ver=3.3.0' media='all' />
    <link rel='stylesheet' id='hello-elementor-header-footer-css'
        href='https://brandmode.1onestrong.com/wp-content/themes/hello-elementor/header-footer.min.css?ver=3.3.0'
        media='all' />
    <link rel='stylesheet' id='elementor-frontend-css'
        href='https://brandmode.1onestrong.com/wp-content/uploads/elementor/css/custom-frontend.min.css?ver=1738903761'
        media='all' />
    <link rel='stylesheet' id='elementor-post-10-css'
        href='https://brandmode.1onestrong.com/wp-content/uploads/elementor/css/post-10.css?ver=1738903761'
        media='all' />
    <link rel='stylesheet' id='widget-image-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/css/widget-image.min.css?ver=3.27.3'
        media='all' />
    <link rel='stylesheet' id='widget-nav-menu-css'
        href='https://brandmode.1onestrong.com/wp-content/uploads/elementor/css/custom-pro-widget-nav-menu.min.css?ver=1738903761'
        media='all' />
    <link rel='stylesheet' id='widget-heading-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/css/widget-heading.min.css?ver=3.27.3'
        media='all' />
    <link rel='stylesheet' id='widget-icon-list-css'
        href='https://brandmode.1onestrong.com/wp-content/uploads/elementor/css/custom-widget-icon-list.min.css?ver=1738903761'
        media='all' />
    <link rel='stylesheet' id='widget-form-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor-pro/assets/css/widget-form.min.css?ver=3.27.2'
        media='all' />
    <link rel='stylesheet' id='swiper-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/lib/swiper/v8/css/swiper.min.css?ver=8.4.5'
        media='all' />
    <link rel='stylesheet' id='e-animation-slideInUp-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/lib/animations/styles/slideInUp.min.css?ver=3.27.3'
        media='all' />
    <link rel='stylesheet' id='widget-spacer-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/css/widget-spacer.min.css?ver=3.27.3'
        media='all' />
    <link rel='stylesheet' id='e-motion-fx-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor-pro/assets/css/modules/motion-fx.min.css?ver=3.27.2'
        media='all' />
    <link rel='stylesheet' id='widget-text-editor-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/css/widget-text-editor.min.css?ver=3.27.3'
        media='all' />
    <link rel='stylesheet' id='e-shapes-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/css/conditionals/shapes.min.css?ver=3.27.3'
        media='all' />
    <link rel='stylesheet' id='elementor-post-7-css'
        href='https://brandmode.1onestrong.com/wp-content/uploads/elementor/css/post-7.css?ver=1738903762'
        media='all' />
    <link rel='stylesheet' id='elementor-post-3790-css'
        href='https://brandmode.1onestrong.com/wp-content/uploads/elementor/css/post-3790.css?ver=1738903762'
        media='all' />
    <link rel='stylesheet' id='elementor-post-1764-css'
        href='https://brandmode.1onestrong.com/wp-content/uploads/elementor/css/post-1764.css?ver=1738903762'
        media='all' />
    <link rel='stylesheet' id='ekit-widget-styles-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementskit-lite/widgets/init/assets/css/widget-styles.css?ver=3.4.0'
        media='all' />
    <link rel='stylesheet' id='ekit-responsive-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementskit-lite/widgets/init/assets/css/responsive.css?ver=3.4.0'
        media='all' />
    <link rel='stylesheet' id='odometer-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementskit-lite/widgets/init/assets/css/odometer-theme-default.css?ver=3.4.0'
        media='all' />
    <link rel='stylesheet' id='google-fonts-1-css'
        href='https://fonts.googleapis.com/css?family=DM+Sans%3A100%2C100italic%2C200%2C200italic%2C300%2C300italic%2C400%2C400italic%2C500%2C500italic%2C600%2C600italic%2C700%2C700italic%2C800%2C800italic%2C900%2C900italic%7CManrope%3A100%2C100italic%2C200%2C200italic%2C300%2C300italic%2C400%2C400italic%2C500%2C500italic%2C600%2C600italic%2C700%2C700italic%2C800%2C800italic%2C900%2C900italic%7CInter+Tight%3A100%2C100italic%2C200%2C200italic%2C300%2C300italic%2C400%2C400italic%2C500%2C500italic%2C600%2C600italic%2C700%2C700italic%2C800%2C800italic%2C900%2C900italic%7CNunito+Sans%3A100%2C100italic%2C200%2C200italic%2C300%2C300italic%2C400%2C400italic%2C500%2C500italic%2C600%2C600italic%2C700%2C700italic%2C800%2C800italic%2C900%2C900italic&#038;display=swap&#038;ver=6.8.1'
        media='all' />
    <link rel='stylesheet' id='elementor-icons-ekiticons-css' href='./css/ekiticons.css' media='all' />
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <script src="https://brandmode.1onestrong.com/wp-includes/js/jquery/jquery.min.js?ver=3.7.1"
        id="jquery-core-js"></script>
    <script src="https://brandmode.1onestrong.com/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1"
        id="jquery-migrate-js"></script>
    <script
        src="https://brandmode.1onestrong.com/wp-content/plugins/template-kit-export/assets/public/template-kit-export-public.js?ver=1.0.23"
        id="template-kit-export-js"></script>
    <link rel="https://api.w.org/" href="https://brandmode.1onestrong.com/wp-json/" />
    <link rel="alternate" title="JSON" type="application/json"
        href="https://brandmode.1onestrong.com/wp-json/wp/v2/pages/7" />
    <link rel="EditURI" type="application/rsd+xml" title="RSD" href="https://brandmode.1onestrong.com/xmlrpc.php?rsd" />
    <meta name="generator" content="WordPress 6.8.1" />
    <link rel="canonical" href="https://brandmode.1onestrong.com/" />
    <link rel='shortlink' href='https://brandmode.1onestrong.com/' />
    <link rel="alternate" title="oEmbed (JSON)" type="application/json+oembed"
        href="https://brandmode.1onestrong.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fbrandmode.1onestrong.com%2F" />
    <link rel="alternate" title="oEmbed (XML)" type="text/xml+oembed"
        href="https://brandmode.1onestrong.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fbrandmode.1onestrong.com%2F&#038;format=xml" />
    <meta name="generator"
        content="Elementor 3.27.3; features: e_font_icon_svg, additional_custom_breakpoints; settings: css_print_method-external, google_font-enabled, font_display-swap">
    <style>
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload),
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload) * {
            background-image: none !important;
        }

        @media screen and (max-height: 1024px) {

            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }

        @media screen and (max-height: 640px) {

            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }
    </style>

    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/scrollreveal"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/ScrollTrigger.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Universal Chat Widget -->
    <script src="universal-chat-widget.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        :root {
            --primary: #2563eb;
            --secondary: #3b82f6;
            --accent: #06b6d4;
            --dark: #1e293b;
            --light: #f8fafc;
            --glass: rgba(255, 255, 255, 0.15);
        }



        .glass-card {
            background: var(--glass);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-radius: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
        }

        .timeline-connector {
            position: relative;
        }

        /* .timeline-connector::after {
            content: '';
            position: absolute;
            width: 4px;
            background: linear-gradient(to bottom, var(--primary), var(--accent));
            top: 100%;
            bottom: -2rem;
            left: 50%;
            transform: translateX(-50%);
        } */

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 25px -5px rgba(59, 130, 246, 0.1), 0 10px 10px -5px rgba(59, 130, 246, 0.04);
        }

        .radar {
            width: 120px;
            height: 120px;
            position: relative;
            background: conic-gradient(from 0deg, rgba(37, 99, 235, 0.1) 0%, rgba(6, 182, 212, 0.3) 30%, rgba(37, 99, 235, 0.1) 70%);
            border-radius: 50%;
            overflow: hidden;
        }

        .radar::before {
            content: '';
            position: absolute;
            width: 80%;
            height: 80%;
            background: var(--glass);
            border-radius: 50%;
            top: 10%;
            left: 10%;
        }

        .radar-scan {
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.5), transparent);
            animation: scan 4s linear infinite;
            transform-origin: left center;
        }

        @keyframes scan {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .signal-dot {
            position: absolute;
            width: 10px;
            height: 10px;
            background: var(--accent);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(6, 182, 212, 0.7);
            }

            70% {
                transform: scale(1);
                box-shadow: 0 0 0 10px rgba(6, 182, 212, 0);
            }

            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(6, 182, 212, 0);
            }
        }

        .phone-ring {
            animation: ring 1.5s ease-in-out infinite;
            transform-origin: 50% 0;
        }

        @keyframes ring {

            0%,
            100% {
                transform: rotate(0deg);
            }

            25% {
                transform: rotate(10deg);
            }

            75% {
                transform: rotate(-10deg);
            }
        }

        .message-bubble {
            position: relative;
            max-width: 220px;
            padding: 12px;
            border-radius: 18px;
            margin: 4px 0;
            animation: slideIn 0.3s ease-out forwards;
            opacity: 0;
        }

        @keyframes slideIn {
            from {
                transform: translateY(10px);
                opacity: 0;
            }

            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .typing-indicator {
            display: flex;
            padding: 10px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 50%;
            margin: 0 2px;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) {
            animation-delay: 0s;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {

            0%,
            60%,
            100% {
                transform: translateY(0);
            }

            30% {
                transform: translateY(-5px);
            }
        }

        .countdown {
            font-family: monospace;
            font-size: 1.2rem;
            color: var(--accent);
        }

        .reactivating {
            animation: glow 2s infinite alternate;
        }

        @keyframes glow {
            0% {
                box-shadow: 0 0 5px -5px var(--accent);
            }

            100% {
                box-shadow: 0 0 20px 5px var(--accent);
            }
        }

        @media (max-width: 768px) {
            .timeline-connector::after {
                left: 1.5rem;
                transform: none;
            }

            .mobile-timeline-item {
                padding-left: 1rem !important;
                padding-right: 1rem !important;
            }
        }

        .elementor-message-danger {
            display: none !important;
        }

        .elementor-message-danger::before {
            display: none;
        }

        /* AI Services Section Styles */
        .ai-services-section {
            padding: 80px 0;
            position: relative;
            overflow: hidden;
        }



        .ai-services-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 1;
        }

        .ai-services-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            align-items: stretch;
            max-width: 1200px;
            margin: 0 auto;
        }

        .ai-service-card {
            padding: 40px 30px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }


        .service-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #2563eb, #3b82f6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            font-size: 2rem;
            color: white;
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
            transition: all 0.3s ease;
        }

        .service-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 15px;
            font-family: 'Inter', sans-serif;
            line-height: 1.2;
        }

        .service-description {
            color: #4a5568;
            line-height: 1.6;
            font-size: 1rem;
            margin: 0;
            flex-grow: 1;
            display: flex;
            align-items: center;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .ai-services-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 25px;
                padding: 0 20px;
            }
        }

        @media (max-width: 992px) {
            .ai-services-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 25px;
            }
        }

        @media (max-width: 768px) {
            .ai-services-section {
                padding: 60px 0;
            }

            .ai-services-container {
                padding: 0 15px;
            }

            .ai-services-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .ai-service-card {
                padding: 30px 25px;
            }

            .service-icon {
                width: 70px;
                height: 70px;
                font-size: 1.8rem;
                margin-bottom: 20px;
            }

            .service-title {
                font-size: 1.3rem;
                margin-bottom: 12px;
            }

            .service-description {
                font-size: 0.95rem;
            }
        }

        @media (max-width: 480px) {
            .ai-services-section {
                padding: 50px 0;
            }

            .ai-service-card {
                padding: 25px 20px;
                border-radius: 15px;
            }

            .service-icon {
                width: 60px;
                height: 60px;
                font-size: 1.6rem;
                margin-bottom: 18px;
            }

            .service-title {
                font-size: 1.2rem;
                margin-bottom: 10px;
            }

            .service-description {
                font-size: 0.9rem;
            }
        }

        /* Animation for AOS */
        [data-aos="fade-up"] {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }

        [data-aos="fade-up"].aos-animate {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
    <link rel="icon" href="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Favicon.png" sizes="32x32" />
    <link rel="icon" href="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Favicon.png" sizes="192x192" />
    <link rel="apple-touch-icon" href="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Favicon.png" />
    <meta name="msapplication-TileImage"
        content="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Favicon.png" />


    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        /* Custom CSS for animated progress bars */
        .progress-container {
            height: 12px;
            background-color: #e5e7eb;
            border-radius: 6px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            border-radius: 6px;
            transition: width 0.5s ease, background-color 0.3s;
        }

        /* Slider thumb style */
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
            box-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
            transition: all 0.2s;
        }

        input[type="range"]::-webkit-slider-thumb:hover {
            background: #2563eb;
            transform: scale(1.1);
        }

        /* Custom animation for result cards */
        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.02);
            }

            100% {
                transform: scale(1);
            }
        }

        .pulse-animate {
            animation: pulse 0.5s ease-out;
        }

        .card-tilt:hover {
            transform: rotate(1deg) translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>

<body
    class="home wp-singular page-template page-template-elementor_header_footer page page-id-7 wp-custom-logo wp-embed-responsive wp-theme-hello-elementor theme-default elementor-default elementor-template-full-width elementor-kit-10 elementor-page elementor-page-7">

    <div data-elementor-type="header" data-elementor-id="3790"
        class="elementor elementor-3790 elementor-location-header" data-elementor-post-type="elementor_library">
        <div class="elementor-element elementor-element-27e5538 e-flex e-con-boxed e-con e-parent" data-id="27e5538"
            data-element_type="container">
            <div class="e-con-inner">
                <div class="elementor-element elementor-element-c931a08 e-con-full e-flex e-con e-child"
                    data-id="c931a08" data-element_type="container">
                    <div class="elementor-element elementor-element-f0c84a9 elementor-widget elementor-widget-theme-site-logo elementor-widget-image"
                        data-id="f0c84a9" data-element_type="widget" data-widget_type="theme-site-logo.default">
                        <div class="elementor-widget-container">
                            <a href="index.html" class="site-logo-link">
                                <img src="img/image.png" alt="Next Level Growth Partners" class="site-logo-img">
                            </a>
                        </div>
                    </div>
                </div>
                <div class=" elementor-element elementor-element-573a350 e-con-full e-flex e-con e-child"
                    data-id="573a350" data-element_type="container">
                    <div class="elementor-element elementor-element-e861c87 elementor-nav-menu__align-center elementor-nav-menu--stretch elementor-nav-menu--dropdown-tablet elementor-nav-menu__text-align-aside elementor-nav-menu--toggle elementor-nav-menu--burger elementor-widget elementor-widget-nav-menu"
                        data-id="e861c87" data-element_type="widget"
                        data-settings="{&quot;submenu_icon&quot;:{&quot;value&quot;:&quot;&lt;i class=\&quot;\&quot;&gt;&lt;\/i&gt;&quot;,&quot;library&quot;:&quot;&quot;},&quot;full_width&quot;:&quot;stretch&quot;,&quot;layout&quot;:&quot;horizontal&quot;,&quot;toggle&quot;:&quot;burger&quot;}"
                        data-widget_type="nav-menu.default">
                        <div class="elementor-widget-container">
                            <nav aria-label="Menu"
                                class="elementor-nav-menu--main elementor-nav-menu__container elementor-nav-menu--layout-horizontal e--pointer-background e--animation-fade">
                                <ul id="menu-1-e861c87" class="elementor-nav-menu">
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom current-menu-ancestor current-menu-parent menu-item-has-children menu-item-3808">
                                        <a class="elementor-item" href="index.html">Home</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3809">
                                        <a class="elementor-item" href="service.html">AI Solutions</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3812">
                                        <a class="elementor-item" href="resource.html">Resources</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3810">
                                        <a class="elementor-item" href="about.html">About</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3811">
                                        <a class="elementor-item" href="faq.html">FAQs</a>
                                    </li>
                                    <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-3798">
                                        <a href="contact.html" class="elementor-item">Contact</a>
                                    </li>
                                </ul>
                            </nav>
                            <div class="elementor-menu-toggle" role="button" tabindex="0" aria-label="Menu Toggle"
                                aria-expanded="false">
                                <i aria-hidden="true" role="presentation"
                                    class="elementor-menu-toggle__icon--open icon icon-burger-menu"></i><svg
                                    aria-hidden="true" role="presentation"
                                    class="elementor-menu-toggle__icon--close e-font-icon-svg e-eicon-close"
                                    viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M742 167L500 408 258 167C246 154 233 150 217 150 196 150 179 158 167 167 154 179 150 196 150 212 150 229 154 242 171 254L408 500 167 742C138 771 138 800 167 829 196 858 225 858 254 829L496 587 738 829C750 842 767 846 783 846 800 846 817 842 829 829 842 817 846 804 846 783 846 767 842 750 829 737L588 500 833 258C863 229 863 200 833 171 804 137 775 137 742 167Z">
                                    </path>
                                </svg>
                            </div>
                            <nav class="elementor-nav-menu--dropdown elementor-nav-menu__container" aria-hidden="true">
                                <ul id="menu-2-e861c87" class="elementor-nav-menu">
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom current-menu-ancestor current-menu-parent menu-item-has-children menu-item-3808">
                                        <a class="elementor-item" tabindex="-1" href="index.html">Home</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3809">
                                        <a class="elementor-item" tabindex="-1" href="service.html">AI Solutions</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3812">
                                        <a class="elementor-item" tabindex="-1" href="resource.html">Resources</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3810">
                                        <a class="elementor-item" tabindex="-1" href="about.html">About</a>
                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-4375">
                                        <a class="elementor-item" tabindex="-1" href="faq.html">
                                            FAQs</a>

                                    </li>
                                    <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-3798">
                                        <a href="contact.html" class="elementor-item" tabindex="-1">Contact</a>
                                    </li>
                            </nav>
                        </div>
                    </div>
                </div>
                <div class="elementor-element elementor-element-bea445e elementor-hidden-tablet elementor-hidden-mobile e-con-full e-flex e-con e-child"
                    data-id="bea445e" data-element_type="container">
                    <div class="elementor-element elementor-element-ebe72f0 elementor-align-right elementor-widget elementor-widget-elementskit-button"
                        data-id="ebe72f0" data-element_type="widget" data-widget_type="elementskit-button.default">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div data-elementor-type="wp-page" data-elementor-id="7" class="elementor elementor-7"
        data-elementor-post-type="page">
        <div class="elementor-element elementor-element-3fc3cf8 e-con-full e-flex e-con e-parent" data-id="3fc3cf8"
            data-element_type="container"
            data-settings="{&quot;background_background&quot;:&quot;video&quot;,&quot;background_video_link&quot;:&quot;https:\/\/youtu.be\/8_VmBZNb9UY&quot;,&quot;background_play_on_mobile&quot;:&quot;yes&quot;,&quot;background_privacy_mode&quot;:&quot;yes&quot;}">
            <div class="elementor-background-video-container">
                <div class="elementor-background-video-embed"></div>
            </div>
            <div class="elementor-element elementor-element-53741ea e-flex e-con-boxed e-con e-child" data-id="53741ea"
                data-element_type="container">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-a569839 e-con-full e-flex e-con e-child"
                        data-id="a569839" data-element_type="container">


                    </div>
                    <div class="elementor-element elementor-element-2283a7e elementor-widget__width-initial elementor-widget elementor-widget-elementskit-heading"
                        data-id="2283a7e" data-element_type="widget" data-widget_type="elementskit-heading.default">
                        <div class="elementor-widget-container">
                            <div class="ekit-wid-con">
                                <div
                                    class="ekit-heading elementskit-section-title-wraper text_center   ekit_heading_tablet-   ekit_heading_mobile-">
                                    <h1 class="ekit-heading--title elementskit-section-title text_fill">
                                        Your
                                        <span><span>24/7 Sales Machine
                                            </span></span><br>
                                        <p class="text-xl tracking-wider">A multi-channel revenue engine supercharged
                                            with AI to autonomously handle and generate demand</p>
                                    </h1>
                                    <div class="service-cta" style="margin-top: 30px;">
                                        <a href="contact.html" class="service-demo-btn"
                                            style="background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; padding: 18px 40px; border-radius: 50px; text-decoration: none; font-weight: 600; display: inline-block; transition: all 0.3s ease; font-size: 1.1rem;">Get
                                            Started</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="elementor-element elementor-element-e7b5ccc e-con-full e-flex e-con e-parent" data-id="e7b5ccc"
            data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
            <div class="elementor-element elementor-element-5eff4c6 e-flex e-con-boxed e-con e-child" data-id="5eff4c6"
                data-element_type="container">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-0972eb6 e-con-full e-flex e-con e-child"
                        data-id="0972eb6" data-element_type="container">
                        <div class="elementor-element elementor-element-96bb3b0 elementor-widget elementor-widget-heading"
                            data-id="96bb3b0" data-element_type="widget" data-widget_type="heading.default">
                            <div class="elementor-widget-container">
                                <h4 class="elementor-heading-title elementor-size-default">Trusted by companies just
                                    like yours to accelerate revenue at scale</h4>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-e934052 elementor-widget elementor-widget-elementskit-client-logo"
                            data-id="e934052" data-element_type="widget"
                            data-widget_type="elementskit-client-logo.default">
                            <div class="elementor-widget-container">
                                <div class="ekit-wid-con">
                                    <div class="elementskit-clients-slider  hover_from_bottom banner_logo_image"
                                        data-config="{&quot;rtl&quot;:false,&quot;arrows&quot;:false,&quot;dots&quot;:false,&quot;autoplay&quot;:true,&quot;speed&quot;:1000,&quot;slidesPerView&quot;:6,&quot;slidesPerGroup&quot;:1,&quot;pauseOnHover&quot;:true,&quot;loop&quot;:false,&quot;breakpoints&quot;:{&quot;320&quot;:{&quot;slidesPerView&quot;:2,&quot;slidesPerGroup&quot;:1,&quot;spaceBetween&quot;:25},&quot;768&quot;:{&quot;slidesPerView&quot;:5,&quot;slidesPerGroup&quot;:1,&quot;spaceBetween&quot;:10},&quot;1024&quot;:{&quot;slidesPerView&quot;:6,&quot;slidesPerGroup&quot;:1,&quot;spaceBetween&quot;:50}}}"
                                        data-direction="hover_from_bottom">
                                        <div class="ekit-main-swiper swiper">
                                            <!-- <div class="swiper-wrapper">
                                                <div class="elementskit-client-slider-item swiper-slide ">
                                                    <div class="swiper-slide-inner">
                                                        <div class="single-client image-switcher" title="Title #1">

                                                            <div class="content-image">

                                                                <img fetchpriority="high" decoding="async" width="992"
                                                                    height="418"
                                                                    src="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-09.png"
                                                                    class="" alt=""
                                                                    srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-09.png 992w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-09-300x126.png 300w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-09-768x324.png 768w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-09-800x337.png 800w"
                                                                    sizes="(max-width: 992px) 100vw, 992px" />
                                                            </div>


                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="elementskit-client-slider-item swiper-slide ">
                                                    <div class="swiper-slide-inner">
                                                        <div class="single-client image-switcher" title="Title #2">

                                                            <div class="content-image">

                                                                <img decoding="async" width="992" height="418"
                                                                    src="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-08.png"
                                                                    class="" alt=""
                                                                    srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-08.png 992w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-08-300x126.png 300w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-08-768x324.png 768w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-08-800x337.png 800w"
                                                                    sizes="(max-width: 992px) 100vw, 992px" />
                                                            </div>


                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="elementskit-client-slider-item swiper-slide ">
                                                    <div class="swiper-slide-inner">
                                                        <div class="single-client image-switcher" title="Title #3">

                                                            <div class="content-image">

                                                                <img loading="lazy" decoding="async" width="992"
                                                                    height="418"
                                                                    src="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-06.png"
                                                                    class="" alt=""
                                                                    srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-06.png 992w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-06-300x126.png 300w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-06-768x324.png 768w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-06-800x337.png 800w"
                                                                    sizes="(max-width: 992px) 100vw, 992px" />
                                                            </div>


                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="elementskit-client-slider-item swiper-slide ">
                                                    <div class="swiper-slide-inner">
                                                        <div class="single-client image-switcher" title="Title #4">

                                                            <div class="content-image">

                                                                <img loading="lazy" decoding="async" width="992"
                                                                    height="418"
                                                                    src="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-05.png"
                                                                    class="" alt=""
                                                                    srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-05.png 992w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-05-300x126.png 300w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-05-768x324.png 768w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-05-800x337.png 800w"
                                                                    sizes="(max-width: 992px) 100vw, 992px" />
                                                            </div>


                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="elementskit-client-slider-item swiper-slide ">
                                                    <div class="swiper-slide-inner">
                                                        <div class="single-client image-switcher" title="Title #5">

                                                            <div class="content-image">

                                                                <img loading="lazy" decoding="async" width="496"
                                                                    height="209"
                                                                    src="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-03.png"
                                                                    class="" alt=""
                                                                    srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-03.png 496w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-03-300x126.png 300w"
                                                                    sizes="(max-width: 496px) 100vw, 496px" />
                                                            </div>


                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="elementskit-client-slider-item swiper-slide ">
                                                    <div class="swiper-slide-inner">
                                                        <div class="single-client image-switcher" title="Title #6">

                                                            <div class="content-image">

                                                                <img loading="lazy" decoding="async" width="496"
                                                                    height="209"
                                                                    src="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-02.png"
                                                                    class="" alt=""
                                                                    srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-02.png 496w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-02-300x126.png 300w"
                                                                    sizes="(max-width: 496px) 100vw, 496px" />
                                                            </div>


                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="elementskit-client-slider-item swiper-slide ">
                                                    <div class="swiper-slide-inner">
                                                        <div class="single-client image-switcher" title="Title #7">

                                                            <div class="content-image">

                                                                <img loading="lazy" decoding="async" width="496"
                                                                    height="209"
                                                                    src="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-04.png"
                                                                    class="" alt=""
                                                                    srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-04.png 496w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-04-300x126.png 300w"
                                                                    sizes="(max-width: 496px) 100vw, 496px" />
                                                            </div>


                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="elementskit-client-slider-item swiper-slide ">
                                                    <div class="swiper-slide-inner">
                                                        <div class="single-client image-switcher" title="Title #8">

                                                            <div class="content-image">

                                                                <img loading="lazy" decoding="async" width="992"
                                                                    height="418"
                                                                    src="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-01.png"
                                                                    class="" alt=""
                                                                    srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-01.png 992w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-01-300x126.png 300w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-01-768x324.png 768w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-01-800x337.png 800w"
                                                                    sizes="(max-width: 992px) 100vw, 992px" />
                                                            </div>


                                                        </div>
                                                    </div>
                                                </div>
                                            </div> -->

                                            <div class="swiper-wrapper">
                                                <div class="elementskit-client-slider-item swiper-slide ">
                                                    <div class="swiper-slide-inner">
                                                        <div class="single-client image-switcher" title="Title #1">

                                                            <div class="content-image">

                                                                <img fetchpriority="high" style="max-width: 125px;"
                                                                    decoding="async" width="992" height="418"
                                                                    src="./img/NIBONY.png" class="" alt=""
                                                                    srcset="./img/NIBONY.png 992w, ./img/NIBONY.png 300w, ./img/NIBONY.png 768w, ./img/NIBONY.png 800w"
                                                                    sizes="(max-width: 992px) 100vw, 992px" />
                                                            </div>


                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="elementskit-client-slider-item swiper-slide ">
                                                    <div class="swiper-slide-inner">
                                                        <div class="single-client image-switcher" title="Title #2">

                                                            <div class="content-image">

                                                                <img decoding="async" style="max-width: 75px;"
                                                                    width="992" height="418"
                                                                    src="./img/DSP_Logo_Final.png" class="" alt=""
                                                                    srcset="./img/DSP_Logo_Final.png 992w, ./img/DSP_Logo_Final.png 300w, ./img/DSP_Logo_Final.png 768w, ./img/DSP_Logo_Final.png 800w"
                                                                    sizes="(max-width: 992px) 100vw, 992px" />
                                                            </div>


                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="elementskit-client-slider-item swiper-slide ">
                                                    <div class="swiper-slide-inner">
                                                        <div class="single-client image-switcher" title="Title #3">

                                                            <div class="content-image">

                                                                <img loading="lazy" style="max-width: 125px;"
                                                                    decoding="async" width="992" height="418"
                                                                    src="./img/SD-Mortgage-Group-LLC-Horizontal.png"
                                                                    class="" alt=""
                                                                    srcset="./img/SD-Mortgage-Group-LLC-Horizontal.png 992w, ./img/SD-Mortgage-Group-LLC-Horizontal.png 300w, ./img/SD-Mortgage-Group-LLC-Horizontal.png 768w, ./img/SD-Mortgage-Group-LLC-Horizontal.png 800w"
                                                                    sizes="(max-width: 992px) 100vw, 992px" />
                                                            </div>


                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="elementskit-client-slider-item swiper-slide ">
                                                    <div class="swiper-slide-inner">
                                                        <div class="single-client image-switcher" title="Title #4">

                                                            <div class="content-image">

                                                                <img loading="lazy" style="max-width: 105px;"
                                                                    decoding="async" width="992" height="418"
                                                                    src="./img/WA-Header-915.png" class="" alt=""
                                                                    srcset="./img/WA-Header-915.png 992w, ./img/WA-Header-915.png 300w, ./img/WA-Header-915.png 768w, ./img/WA-Header-915.png 800w"
                                                                    sizes="(max-width: 992px) 100vw, 992px" />
                                                            </div>


                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="elementskit-client-slider-item swiper-slide ">
                                                    <div class="swiper-slide-inner">
                                                        <div class="single-client image-switcher" title="Title #5">

                                                            <div class="content-image">

                                                                <img loading="lazy" style="max-width: 65px;"
                                                                    decoding="async" width="496" height="209"
                                                                    src="./img/tsg.png" class="" alt=""
                                                                    srcset="./img/tsg.png 496w, ./img/tsg.png 300w"
                                                                    sizes="(max-width: 496px) 100vw, 496px" />
                                                            </div>


                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="elementskit-client-slider-item swiper-slide ">
                                                    <div class="swiper-slide-inner">
                                                        <div class="single-client image-switcher" title="Title #6">

                                                            <div class="content-image">

                                                                <!-- <img loading="lazy" style="max-width: 125px;" decoding="async" width="496" height="209" src="./img/logo-1.png" class="" alt="" srcset="/img/logo-1.png 496w, /img/logo-1.png 300w" sizes="(max-width: 496px) 100vw, 496px" />										</div> -->
                                                                <img loading="lazy" style="max-width: 65px;"
                                                                    decoding="async" width="496" height="209"
                                                                    src="./img/logo-1.png" class="" alt=""
                                                                    srcset="./img/logo-1.png 496w, ./img/logo-1.png 300w"
                                                                    sizes="(max-width: 496px) 100vw, 496px" />
                                                            </div>


                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- <div class="elementskit-client-slider-item swiper-slide ">
                                        <div class="swiper-slide-inner">
                                          <div class="single-client image-switcher" title="Title #7">
                                            
                                              <div class="content-image">
                          
                                                <img loading="lazy" decoding="async" width="496" height="209" src="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-04.png" class="" alt="" srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-04.png 496w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-04-300x126.png 300w" sizes="(max-width: 496px) 100vw, 496px" />										</div>
                          
                                            
                                          </div>
                                        </div>
                                      </div>
                                                <div class="elementskit-client-slider-item swiper-slide ">
                                        <div class="swiper-slide-inner">
                                          <div class="single-client image-switcher" title="Title #8">
                                            
                                              <div class="content-image">
                          
                                                <img loading="lazy" decoding="async" width="992" height="418" src="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-01.png" class="" alt="" srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-01.png 992w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-01-300x126.png 300w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-01-768x324.png 768w, https://brandmode.1onestrong.com/wp-content/uploads/2024/02/Logo-01-800x337.png 800w" sizes="(max-width: 992px) 100vw, 992px" />										</div>
                          
                                            
                                          </div>
                                        </div>
                                      </div> -->
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- AI Services Features Section -->
            <section class="ai-services-section">
                <div class="ai-services-container">
                    <div class="ai-services-grid">
                        <!-- Lead Engagement -->
                        <div class="ai-service-card" data-aos="fade-up" data-aos-delay="100">
                            <div class="service-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h3 class="service-title">Engage Leads</h3>
                            <p class="service-description">Instantly engage customers with our powerful
                                AI + SMS marketing campaign.</p>
                        </div>

                        <!-- Sales Qualifying -->
                        <div class="ai-service-card" data-aos="fade-up" data-aos-delay="200">
                            <div class="service-icon">
                                <i class="fas fa-filter"></i>
                            </div>
                            <h3 class="service-title">Qualify Prospects</h3>
                            <p class="service-description">Weed out the bad leads so you can focus on
                                the valuable ones.</p>
                        </div>

                        <!-- Lead Nurturing -->
                        <div class="ai-service-card" data-aos="fade-up" data-aos-delay="300">
                            <div class="service-icon">
                                <i class="fas fa-seedling"></i>
                            </div>
                            <h3 class="service-title">Nurture Connections</h3>
                            <p class="service-description">Never loose another opportunity with
                                automated follow ups to touch contacts.</p>
                        </div>

                        <!-- Appointments Set -->
                        <div class="ai-service-card" data-aos="fade-up" data-aos-delay="400">
                            <div class="service-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <h3 class="service-title">Generate Sales
                            </h3>
                            <p class="service-description">Establish multi-channel outreach to your ideal customers on
                                autopilot.</p>
                        </div>

                        <!-- Intel Collection -->
                        <!-- <div class="ai-service-card" data-aos="fade-up" data-aos-delay="500">
                        <div class="service-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="service-title">Intel Collection</h3>
                        <p class="service-description">Dominate sales calls with customer insights uploaded right into your CRM.</p>
                    </div> -->
                    </div>
                </div>
            </section>



            <div class="elementor-element elementor-element-95aa844 e-flex e-con-boxed e-con e-child" data-id="95aa844"
                data-element_type="container">
                <div class="e-con-inner" style="margin-bottom: -100px;">
                    <div class="elementor-element elementor-element-50caa26 e-con-full e-flex e-con e-child"
                        data-id="50caa26" data-element_type="container">
                        <div class="elementor-element elementor-element-e19a470 e-con-full e-flex e-con e-child"
                            data-id="e19a470" data-element_type="container">


                        </div>
                        <div class="elementor-element elementor-element-c4575d5 elementor-widget elementor-widget-elementskit-heading"
                            data-id="c4575d5" data-element_type="widget" data-widget_type="elementskit-heading.default">
                            <div class="elementor-widget-container">
                                <div class="ekit-wid-con">
                                    <div
                                        class="ekit-heading elementskit-section-title-wraper text_center   ekit_heading_tablet-   ekit_heading_mobile-">
                                        <h2 class="ekit-heading--title elementskit-section-title text_fill">
                                            The Growth Partner Engine
                                        </h2>
                                        <div class='ekit-heading__description'>
                                            <p>Power your entire sales process with powerful AI solutions to unlock
                                                untapped potential in your business</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
            rel="stylesheet">
        <style>
            :root {
                --primary: #4F46E5;
                --secondary: #10B981;
                --step-1: #6366F1;
                --step-2: #8B5CF6;
                --step-3: #EC4899;
                --step-4: #F97316;
                --step-5: #F59E0B;
                --step-6: #84CC16;
            }

            body {
                font-family: 'Inter', sans-serif;
                background-color: #F9FAFB;
            }

            .step-card {
                transition: all 0.3s ease;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.05);
            }

            .step-card:hover {
                transform: translateY(-3px);
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }

            .step-connector {
                position: relative;
                height: 30px;
                width: 2px;
                margin-left: 24px;
                background: linear-gradient(to bottom, rgba(156, 163, 175, 0.4), rgba(156, 163, 175, 0.1));
            }

            .step-connector::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: -4px;
                width: 10px;
                height: 10px;
                border-radius: 50%;
                background-color: rgba(156, 163, 175, 0.4);
            }

            .accordion-content {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease-out;
            }

            .accordion-content.open {
                max-height: 500px;
                transition: max-height 0.5s ease-in;
            }

            .step-1 {
                border-left-color: var(--step-1);
                backdrop-filter: blur(16px);
                background: var(--glass);
            }

            .step-2 {
                border-left-color: var(--step-2);
                backdrop-filter: blur(16px);
                background: var(--glass);
            }

            .step-3 {
                border-left-color: var(--step-3);
                backdrop-filter: blur(16px);
                background: var(--glass);
            }

            .step-4 {
                border-left-color: var(--step-4);
                backdrop-filter: blur(16px);
                background: var(--glass);
            }

            .step-5 {
                border-left-color: var(--step-5);
                backdrop-filter: blur(16px);
                background: var(--glass);
            }

            .step-6 {
                border-left-color: var(--step-6);
                backdrop-filter: blur(16px);
                background: var(--glass);
            }

            .step-badge-1 {
                background-color: var(--step-1);
            }

            .step-badge-2 {
                background-color: var(--step-2);
            }

            .step-badge-3 {
                background-color: var(--step-3);
            }

            .step-badge-4 {
                background-color: var(--step-4);
            }

            .step-badge-5 {
                background-color: var(--step-5);
            }

            .step-badge-6 {
                background-color: var(--step-6);
            }

            .leading-6 {
                margin-top: -10px;
            }

            .drop-text-style {
                color: #a7a7a7;
            }

            /* Growth Partner Engine Horizontal Layout */
            .growth-engine-container {
                max-width: 100%;
                margin: 0 auto;
                padding: 40px 20px;
                overflow: hidden;
            }

            .growth-engine-scroll-wrapper {
                overflow-x: auto;
                overflow-y: hidden;
                padding-bottom: 20px;
                scrollbar-width: none;
                /* Firefox */
                -ms-overflow-style: none;
                /* Internet Explorer 10+ */
            }

            .growth-engine-scroll-wrapper::-webkit-scrollbar {
                display: none;
                /* Chrome, Safari, Opera */
            }

            .growth-engine-scroll-wrapper::-webkit-scrollbar {
                height: 8px;
            }

            .growth-engine-scroll-wrapper::-webkit-scrollbar-track {
                background: #f1f5f9;
                border-radius: 10px;
            }

            .growth-engine-scroll-wrapper::-webkit-scrollbar-thumb {
                background: #818CF8;
                border-radius: 10px;
            }

            .growth-engine-horizontal-flow {
                display: flex;
                gap: 30px;
                min-width: max-content;
                align-items: flex-start;
                padding: 20px 0;
            }

            .growth-engine-horizontal-flow .flow-step {
                flex: 0 0 350px;
                position: relative;
            }

            .growth-engine-horizontal-flow .flow-step:not(:last-child)::after {
                content: '';
                position: absolute;
                top: 50%;
                right: -45px;
                transform: translateY(-50%);
                width: 30px;
                height: 2px;
                background: linear-gradient(to right, #818CF8, #A855F7);
                z-index: 1;
            }

            .growth-engine-horizontal-flow .flow-step:not(:last-child)::before {
                content: '';
                position: absolute;
                top: 50%;
                right: -50px;
                transform: translateY(-50%);
                width: 0;
                height: 0;
                border-left: 10px solid #A855F7;
                border-top: 6px solid transparent;
                border-bottom: 6px solid transparent;
                z-index: 2;
            }

            @media (max-width: 1200px) {
                .growth-engine-horizontal-flow .flow-step {
                    flex: 0 0 320px;
                }
            }

            @media (max-width: 768px) {
                .growth-engine-container {
                    padding: 20px 10px;
                }

                .growth-engine-horizontal-flow {
                    gap: 20px;
                }

                .growth-engine-horizontal-flow .flow-step {
                    flex: 0 0 280px;
                }

                .growth-engine-horizontal-flow .flow-step:not(:last-child)::after {
                    right: -35px;
                    width: 20px;
                }

                .growth-engine-horizontal-flow .flow-step:not(:last-child)::before {
                    right: -40px;
                    border-left: 8px solid #A855F7;
                    border-top: 5px solid transparent;
                    border-bottom: 5px solid transparent;
                }
            }

            @media (max-width: 767px) {
                .space-y-8 {
                    margin-top: 60px;
                    /* Adjust to your need */
                    padding-left: 15px;
                    padding-right: 15px;
                }
            }
        </style>
        <!-- Growth Partner Engine Horizontal Layout -->
        <div class="growth-engine-container">
            <div class="growth-engine-scroll-wrapper">
                <div class="growth-engine-horizontal-flow">
                    <!-- Step 1 -->
                    <div class="flow-step">
                        <div class="step-card bg-white rounded-lg overflow-hidden border-l-4 border-solid step-1 cursor-pointer"
                            onclick="toggleAccordion('step1')">
                            <div class="p-6">
                                <div class="flex items-start">
                                    <div
                                        class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-md step-badge-1 text-white font-bold text-sm">
                                        1
                                    </div>
                                    <div class="ml-4 flex items-center justify-between w-full">
                                        <div class="ml-4 flex justify-between items-start w-full">
                                            <div>
                                                <h3 class="text-lg leading-6 font-medium text-gray-900">Lead
                                                    Generation</h3>
                                                <p class="mt-1 drop-text-style text-sm text-gray-500">
                                                    Our team identifies your ideal customer profile and scrapes their
                                                    contact info from where they hangout online to be verified and
                                                    enriched.
                                                </p>
                                            </div>
                                            <button
                                                onclick="document.getElementById('lead-generation').scrollIntoView({ behavior: 'smooth' });"
                                                class="w-7 h-7 flex items-center justify-center rounded-full bg-indigo-100 hover:bg-indigo-200 text-indigo-600 text-xs transition-all"
                                                title="Go to Details" style="    margin-right: 10px;">
                                                <i class="fas fa-arrow-up-right-from-square"></i>
                                            </button>
                                        </div>

                                    </div>

                                    <div class="ml-auto">
                                        <!-- <i id="step1-icon" class="fas fa-chevron-down text-gray-400 transition-transform duration-300"></i> -->
                                    </div>
                                </div>
                            </div>
                            <div id="step1-content" class="accordion-content px-6 -mt-2">
                                <div class="pl-14 pt-3 border-t border-gray-200" style="margin-bottom: 12px;">
                                    <p class="text-sm drop-text-style text-gray-600">Our system ensures your business
                                        targets only the most accurate data and highest-intent decision-makers—filtering
                                        contacts by company size, job title, location, technographics, and real-time
                                        intent signals.
                                    </p>
                                    <div class="mt-4">
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 mr-2">
                                            <i class="fas fa-database mr-1"></i> Data Scraping
                                        </span>
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 mr-2">
                                            <i class="fas fa-sync-alt mr-1"></i> Sync
                                        </span>
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                            <i class="fas fa-magic mr-1"></i> Enrichment
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="step-connector"></div> -->
                    </div>

                    <!-- Step 2 -->
                    <div class="relative flow-step">
                        <div class="step-card bg-white rounded-lg overflow-hidden border-l-4 border-solid step-2 cursor-pointer"
                            onclick="toggleAccordion('step2')">
                            <div class="p-6">
                                <div class="flex items-start">
                                    <div
                                        class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-md step-badge-2 text-white font-bold text-sm">
                                        2
                                    </div>
                                    <div class="ml-4 flex justify-between items-start w-full">
                                        <div>
                                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                                Automated Outreach</h3>
                                            <p class="mt-1 drop-text-style text-sm text-gray-500">
                                                Save countless hours manually mining for leads by automating outreach to
                                                your highest-intent prospects—delivered through the marketing channel
                                                where they’re most likely to respond.
                                            </p>
                                        </div>
                                        <button
                                            onclick="document.getElementById('cold-outreach').scrollIntoView({ behavior: 'smooth' });"
                                            class="w-7 h-7 flex items-center justify-center rounded-full bg-indigo-100 hover:bg-indigo-200 text-indigo-600 text-xs transition-all"
                                            title="Go to Details" style="    margin-right: 10px;">
                                            <i class="fas fa-arrow-up-right-from-square"></i>
                                        </button>
                                    </div>

                                </div>
                            </div>
                            <div id="step2-content" class="accordion-content px-6 -mt-2">
                                <div class="pl-14 pt-3 border-t border-gray-200" style="margin-bottom: 12px;">
                                    <p class="text-sm drop-text-style text-gray-600"> Personalized sequences that adapt
                                        based on open and response rates. Our system analyzes responses to optimize
                                        timing and content to produce higher conversion rates.</p>
                                    <div class="mt-4">
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 mr-2">
                                            <i class="fas fa-envelope mr-1"></i> Email
                                        </span>
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 mr-2">
                                            <i class="fas fa-comment-alt mr-1"></i> SMS
                                        </span>
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                            <i class="fab fa-linkedin mr-1"></i> LinkedIn
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="step-connector"></div> -->
                    </div>

                    <!-- Step 3 -->
                    <div class="relative flow-step">
                        <div class="step-card bg-white rounded-lg overflow-hidden border-l-4 border-solid step-3 cursor-pointer"
                            onclick="toggleAccordion('step3')">
                            <div class="p-6">
                                <div class="flex items-start">
                                    <div
                                        class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-md step-badge-3 text-white font-bold text-sm">
                                        3
                                    </div>
                                    <div class="ml-4 flex justify-between items-start w-full">
                                        <div>
                                            <h3 class="text-lg leading-6 font-medium text-gray-900">Speed to
                                                Lead</h3>
                                            <p class="mt-1 drop-text-style text-sm text-gray-500">Have an AI Agent
                                                custom to your business respond instantly to an incoming contact through
                                                ads, landing pages, forms, and more.
                                            </p>
                                        </div>
                                        <button
                                            onclick="document.getElementById('lead-conversion').scrollIntoView({ behavior: 'smooth' });"
                                            class="w-7 h-7 flex items-center justify-center rounded-full bg-indigo-100 hover:bg-indigo-200 text-indigo-600 text-xs transition-all"
                                            title="Go to Details" style="    margin-right: 10px;">
                                            <i class="fas fa-arrow-up-right-from-square"></i>
                                        </button>
                                    </div>

                                </div>
                            </div>
                            <div id="step3-content" class="accordion-content px-6 -mt-2">
                                <div class="pl-14 pt-3 border-t border-gray-200" style="margin-bottom: 12px;">
                                    <p class="text-sm drop-text-style text-gray-600">Be the business that gets the
                                        business by being the first to respond to a customer in need. Your custom agent
                                        can answer common questions, qualify the lead, and schedule meetings.</p>
                                    <div class="mt-4">
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800 mr-2">
                                            <i class="fas fa-bolt mr-1"></i> Instant Response
                                        </span>
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800 mr-2">
                                            <i class="fas fa-robot mr-1"></i> AI Assistant
                                        </span>
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">
                                            <i class="fas fa-stopwatch mr-1"></i>
                                            <span> second response </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="step-connector"></div> -->
                    </div>

                    <!-- Step 4 -->
                    <div class="relative flow-step">
                        <div class="step-card bg-white rounded-lg overflow-hidden border-l-4 border-solid step-4 cursor-pointer"
                            onclick="toggleAccordion('step4')">
                            <div class="p-6">
                                <div class="flex items-start">
                                    <div
                                        class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-md step-badge-4 text-white font-bold text-sm">
                                        4
                                    </div>
                                    <div class="ml-4 flex justify-between items-start w-full">
                                        <div>
                                            <h3 class="text-lg leading-6 font-medium text-gray-900">Database
                                                Reactivation</h3>
                                            <p class="mt-1 drop-text-style text-sm text-gray-500">Re-engage cold leads
                                                within your CRM, converting forgotten prospects into new found revenue.
                                            </p>
                                        </div>
                                        <button
                                            onclick="document.getElementById('database-reactivation').scrollIntoView({ behavior: 'smooth' });"
                                            class="w-7 h-7 flex items-center justify-center rounded-full bg-indigo-100 hover:bg-indigo-200 text-indigo-600 text-xs transition-all"
                                            title="Go to Details" style="    margin-right: 10px;">
                                            <i class="fas fa-arrow-up-right-from-square"></i>
                                        </button>
                                    </div>

                                </div>
                            </div>
                            <div id="step4-content" class="accordion-content px-6 -mt-2">
                                <div class="pl-14 pt-3 border-t border-gray-200" style="margin-bottom: 12px;">
                                    <p class="text-sm drop-text-style text-gray-600">Establish targeted campaigns to
                                        each customer demographic within your business and deliver personalized messages
                                        to rekindle their interest in your services.
                                    </p>
                                    <div class="mt-4">
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 mr-2">
                                            <i class="fas fa-fire mr-1"></i> Reactivation
                                        </span>
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 mr-2">
                                            <i class="fas fa-snowflake mr-1"></i> Cold Leads
                                        </span>
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                            <i class="fas fa-chart-line mr-1"></i> Win-back Strategy
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="step-connector"></div> -->
                    </div>

                    <!-- Step 5 -->
                    <div class="relative flow-step">
                        <div class="step-card bg-white rounded-lg overflow-hidden border-l-4 border-solid step-5 cursor-pointer"
                            onclick="toggleAccordion('step5')">
                            <div class="p-6">
                                <div class="flex items-start">
                                    <div
                                        class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-md step-badge-5 text-white font-bold text-sm">
                                        5
                                    </div>
                                    <div class="ml-4 flex justify-between items-start w-full">
                                        <div>
                                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                                Appointment Setting</h3>
                                            <p class="mt-1 drop-text-style text-sm text-gray-500">Have warm leads ready
                                                to do business booked directly into your team’s calendar with reminders
                                                and confirmations to ensure they are present on the call.</p>
                                        </div>
                                        <button
                                            onclick="document.getElementById('appointment-setting').scrollIntoView({ behavior: 'smooth' });"
                                            class="w-7 h-7 flex items-center justify-center rounded-full bg-indigo-100 hover:bg-indigo-200 text-indigo-600 text-xs transition-all"
                                            title="Go to Details" style="    margin-right: 10px;">
                                            <i class="fas fa-arrow-up-right-from-square"></i>
                                        </button>
                                    </div>

                                </div>
                            </div>
                            <div id="step5-content" class="accordion-content px-6 -mt-2">
                                <div class="pl-14 pt-3 border-t border-gray-200" style="margin-bottom: 12px;">
                                    <p class="text-sm drop-text-style text-gray-600">Our system integrates
                                        with your calendar to instantly book qualified meetings. Automated
                                        reminders and confirmations reduce no-shows by up to 70%.</p>
                                    <div class="mt-4">
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mr-2">
                                            <i class="fas fa-calendar-check mr-1"></i> Scheduling
                                        </span>
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mr-2">
                                            <i class="fas fa-bell mr-1"></i> Reminders
                                        </span>
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            <i class="fas fa-redo mr-1"></i> Follow-ups
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="step-connector"></div> -->
                    </div>

                    <!-- Step 6 -->
                    <!-- <div class="relative flow-step">
                    <div class="step-card bg-white rounded-lg overflow-hidden border-l-4 border-solid step-6 cursor-pointer"
                        onclick="toggleAccordion('step6')">
                        <div class="p-6">
                            <div class="flex items-start">
                                <div
                                    class="flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-md step-badge-6 text-white font-bold text-sm">
                                    6
                                </div>
                                <div class="ml-4 flex justify-between items-start w-full">
                                    <div>
                                        <h3 class="text-lg leading-6 font-medium text-gray-900">AI Voice
                                            Receptionist + Chat Widget + Cart Recovery</h3>
                                        <p class="mt-1 drop-text-style text-sm text-gray-500">Answer
                                            calls, qualify leads via chat, and recover abandoned carts
                                            automatically.</p>
                                    </div>
                                    <button
                                        onclick="document.getElementById('voice-ai').scrollIntoView({ behavior: 'smooth' });"
                                        class="w-7 h-7 flex items-center justify-center rounded-full bg-indigo-100 hover:bg-indigo-200 text-indigo-600 text-xs transition-all"
                                        title="Go to Details" style="    margin-right: 10px;">
                                        <i class="fas fa-arrow-up-right-from-square"></i>
                                    </button>
                                </div>

                            </div>
                        </div>
                        <div id="step6-content" class="accordion-content px-6 -mt-2">
                            <div class="pl-14 pt-3 border-t border-gray-200" style="margin-bottom: 12px;">
                                <p class="text-sm drop-text-style text-gray-600">Our 24/7 AI assistant
                                    handles incoming calls with human-like conversations, qualifies
                                    leads through intelligent chat, and recovers lost sales with
                                    personalized cart recovery sequences.</p>
                                <div class="mt-4">
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                                        <i class="fas fa-phone-alt mr-1"></i> Voice AI
                                    </span>
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                                        <i class="fas fa-comments mr-1"></i> Smart Chat
                                    </span>
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-shopping-cart mr-1"></i> Cart Recovery
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
                </div>
                <div class='ekit-heading__description  text-center'>
                    <p>Next Level Growth Partner’s delivers compounding growth to your revenue at every step of the
                        customer lifecycle</p>
                </div>
                <div class="service-cta" style="margin-top: 30px;  text-align: center;">
                    <a href="contact.html" class="service-demo-btn"
                        style="background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; padding: 18px 40px; border-radius: 50px; text-decoration: none; font-weight: 600; display: inline-block; transition: all 0.3s ease; font-size: 1.1rem;">Get
                        Started</a>
                </div>
            </div>

            <script>
                function toggleAccordion(stepId) {
                    const content = document.getElementById(`${stepId}-content`);
                    const icon = document.getElementById(`${stepId}-icon`);

                    content.classList.toggle('open');
                    icon.classList.toggle('rotate-180');
                }
            </script>

            <div class="elementor-element elementor-element-95aa844 e-flex e-con-boxed e-con e-child" data-id="95aa844"
                data-element_type="container">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-50caa26 e-con-full e-flex e-con e-child"
                        data-id="50caa26" data-element_type="container">
                        <div class="elementor-element elementor-element-e19a470 e-con-full e-flex e-con e-child"
                            data-id="e19a470" data-element_type="container">
                        </div>
                        <div class="elementor-element elementor-element-c4575d5 elementor-widget elementor-widget-elementskit-heading"
                            data-id="c4575d5" data-element_type="widget" data-widget_type="elementskit-heading.default">
                            <div class="elementor-widget-container">
                                <div class="ekit-wid-con">
                                    <div
                                        class="ekit-heading elementskit-section-title-wraper text_center   ekit_heading_tablet-   ekit_heading_mobile-">
                                        <h2 class="ekit-heading--title elementskit-section-title text_fill">
                                            Convert
                                            <span><span>every Opportunity</span></span>
                                        </h2>
                                        <div class='ekit-heading__description'>
                                            <p>Next Level Growth Partner’s AI solutions automatically book appointments
                                                across all channels—calls, text, web-chat, online widgets, and
                                                third-party platforms—directly into your CRM. By optimizing your lead
                                                engagement and conversion our AI-powered systems enable your team to
                                                focus solely on mission-critical interactions that drive growth</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="elementor-element elementor-element-87ddf11 e-con-full e-flex e-con e-parent" data-id="87ddf11"
                data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                <div class="elementor-element elementor-element-f352fab e-con-full e-flex e-con e-child"
                    data-id="f352fab" data-element_type="container"
                    data-settings="{&quot;background_background&quot;:&quot;classic&quot;,&quot;background_motion_fx_motion_fx_scrolling&quot;:&quot;yes&quot;,&quot;background_motion_fx_translateY_effect&quot;:&quot;yes&quot;,&quot;background_motion_fx_devices&quot;:[&quot;desktop&quot;,&quot;laptop&quot;],&quot;background_motion_fx_translateY_speed&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:4,&quot;sizes&quot;:[]},&quot;background_motion_fx_translateY_affectedRange&quot;:{&quot;unit&quot;:&quot;%&quot;,&quot;size&quot;:&quot;&quot;,&quot;sizes&quot;:{&quot;start&quot;:0,&quot;end&quot;:100}}}">
                    <div class="elementor-element elementor-element-80c7186 elementor-widget elementor-widget-spacer"
                        data-id="80c7186" data-element_type="widget" data-widget_type="spacer.default">
                        <div class="elementor-widget-container">
                            <div class="elementor-spacer">
                                <div class="elementor-spacer-inner"></div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>


            <div class="elementor-element elementor-element-2b45c02 e-con-full e-flex e-con e-parent" data-id="2b45c02"
                data-element_type="container">
                <div class="elementor-element elementor-element-ffd1bbe e-con-full e-flex e-con e-child"
                    data-id="ffd1bbe" data-element_type="container"
                    data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                    <div class="elementor-element elementor-element-b95db8d e-con-full e-flex e-con e-child"
                        data-id="b95db8d" data-element_type="container"
                        data-settings="{&quot;shape_divider_top&quot;:&quot;arrow&quot;,&quot;shape_divider_bottom&quot;:&quot;arrow&quot;}">
                        <div class="elementor-shape elementor-shape-top" data-negative="false">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 700 10" preserveAspectRatio="none">
                                <path class="elementor-shape-fill" d="M350,10L340,0h20L350,10z" />
                            </svg>
                        </div>
                        <div class="elementor-shape elementor-shape-bottom" data-negative="false">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 700 10" preserveAspectRatio="none">
                                <path class="elementor-shape-fill" d="M350,10L340,0h20L350,10z" />
                            </svg>
                        </div>
                        <div class="elementor-element elementor-element-9126eda e-con-full e-flex e-con e-child"
                            data-id="9126eda" data-element_type="container">
                            <div class="elementor-element elementor-element-ac90630 e-con-full e-flex e-con e-child"
                                data-id="ac90630" data-element_type="container">


                            </div>
                            <div class="elementor-element elementor-element-789718c elementor-widget elementor-widget-elementskit-heading"
                                data-id="789718c" data-element_type="widget"
                                data-widget_type="elementskit-heading.default">
                                <div class="elementor-widget-container">
                                    <div class="ekit-wid-con">
                                        <div
                                            class="ekit-heading elementskit-section-title-wraper text_center   ekit_heading_tablet-   ekit_heading_mobile-">
                                            <h2 class="ekit-heading--title elementskit-section-title text_fill">
                                                <span><span>A Proven Process</span></span>
                                            </h2>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-e8e3297 elementor-widget__width-initial elementor-widget elementor-widget-text-editor"
                                data-id="e8e3297" data-element_type="widget" data-widget_type="text-editor.default">
                                <div class="elementor-widget-container">
                                    <p>We begin by understanding your vision, goals, and operational realities—including
                                        requirements, workflows, and constraints. Through collaborative discovery and
                                        in-depth research, we establish a strategic foundation that sets your project up
                                        for long-term success.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-7cde1bd e-grid e-con-full e-con e-child process-flow-container"
                            data-id="7cde1bd" data-element_type="container">
                            <div class="timeline-progress-vertical"></div>
                            <div class="elementor-element elementor-element-0bfb753 ekit-equal-height-disable elementor-invisible elementor-widget elementor-widget-elementskit-icon-box process-step-item"
                                data-id="0bfb753" data-element_type="widget" data-step="1"
                                data-settings="{&quot;_animation&quot;:&quot;slideInUp&quot;,&quot;_animation_delay&quot;:150}"
                                data-widget_type="elementskit-icon-box.default">
                                <div class="elementor-widget-container">
                                    <div class="ekit-wid-con"> <!-- link opening -->
                                        <a href="#" class="ekit_global_links">
                                            <!-- end link opening -->

                                            <div
                                                class="elementskit-infobox text-left text-left icon-lef-right-aligin elementor-animation- media  ">
                                                <div class="elementskit-box-header elementor-animation-">
                                                    <div class="elementskit-info-box-icon  text-center">
                                                        <i aria-hidden="true"
                                                            class="elementkit-infobox-icon icon icon-shuffle-arrow"></i>
                                                    </div>
                                                </div>
                                                <div class="box-body">
                                                    <h3 class="elementskit-info-box-title">
                                                        Discovery </h3>
                                                    <p>We begin by understanding your vision, goals, requirements as
                                                        well as your business operations and limitations. Through
                                                        collaborative discussions and research, we lay the foundation
                                                        for your project's success.</p>
                                                </div>


                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="elementor-element elementor-element-8bd6906 ekit-equal-height-disable elementor-invisible elementor-widget elementor-widget-elementskit-icon-box process-step-item"
                                data-id="8bd6906" data-element_type="widget" data-step="3"
                                data-settings="{&quot;_animation&quot;:&quot;slideInUp&quot;,&quot;_animation_delay&quot;:250}"
                                data-widget_type="elementskit-icon-box.default">
                                <div class="elementor-widget-container">
                                    <div class="ekit-wid-con"> <!-- link opening -->
                                        <a href="#" class="ekit_global_links">
                                            <!-- end link opening -->

                                            <div
                                                class="elementskit-infobox text-left text-left icon-lef-right-aligin elementor-animation- media  ">
                                                <div class="elementskit-box-header elementor-animation-">
                                                    <div class="elementskit-info-box-icon  text-center">
                                                        <i aria-hidden="true"
                                                            class="elementkit-infobox-icon icon icon-Design-3"></i>
                                                    </div>
                                                </div>
                                                <div class="box-body">
                                                    <h3 class="elementskit-info-box-title">
                                                        Development </h3>
                                                    <p>Our team builds your custom AI Agents with care and agility. We
                                                        build, test, and iterate to ensure your solution meets the
                                                        highest standards of quality and performance.</p>
                                                </div>


                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-96be2b5 ekit-equal-height-disable elementor-invisible elementor-widget elementor-widget-elementskit-icon-box process-step-item"
                                data-id="96be2b5" data-element_type="widget" data-step="4"
                                data-settings="{&quot;_animation&quot;:&quot;slideInUp&quot;,&quot;_animation_delay&quot;:300}"
                                data-widget_type="elementskit-icon-box.default">
                                <div class="elementor-widget-container">
                                    <div class="ekit-wid-con"> <!-- link opening -->
                                        <a href="#" class="ekit_global_links">
                                            <!-- end link opening -->

                                            <div
                                                class="elementskit-infobox text-left text-left icon-lef-right-aligin elementor-animation- media  ">
                                                <div class="elementskit-box-header elementor-animation-">
                                                    <div class="elementskit-info-box-icon  text-center">
                                                        <i aria-hidden="true"
                                                            class="elementkit-infobox-icon icon icon-menu-6"></i>
                                                    </div>
                                                </div>
                                                <div class="box-body">
                                                    <h3 class="elementskit-info-box-title">
                                                        Deployment </h3>
                                                    <p>We carefully launch your solution, ensuring a smooth transition
                                                        to production. Our team provides ongoing support and
                                                        optimization to keep your system running at its best.</p>
                                                </div>


                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-96be2b5 ekit-equal-height-disable elementor-invisible elementor-widget elementor-widget-elementskit-icon-box process-step-item"
                                data-id="96be2b5" data-element_type="widget" data-step="4"
                                data-settings="{&quot;_animation&quot;:&quot;slideInUp&quot;,&quot;_animation_delay&quot;:300}"
                                data-widget_type="elementskit-icon-box.default">
                                <div class="elementor-widget-container">
                                    <div class="ekit-wid-con"> <!-- link opening -->
                                        <a href="#" class="ekit_global_links">
                                            <!-- end link opening -->

                                            <div
                                                class="elementskit-infobox text-left text-left icon-lef-right-aligin elementor-animation- media  ">
                                                <div class="elementskit-box-header elementor-animation-">
                                                    <div class="elementskit-info-box-icon  text-center">
                                                        <i aria-hidden="true"
                                                            class="elementkit-infobox-icon icon icon-menu-6"></i>
                                                    </div>
                                                </div>
                                                <div class="box-body">
                                                    <h3 class="elementskit-info-box-title">
                                                        Delivery </h3>
                                                    <p>Experience streamlined operations, improved systems, and new
                                                        found revenue from an influx of sales.</p>
                                                </div>


                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-2ee0446 e-con-full e-flex e-con e-child"
                            data-id="2ee0446" data-element_type="container">
                            <div class="elementor-element elementor-element-0637237 elementor-widget elementor-widget-spacer"
                                data-id="0637237" data-element_type="widget" data-widget_type="spacer.default">
                                <div class="elementor-widget-container">
                                    <div class="elementor-spacer">
                                        <div class="elementor-spacer-inner"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="elementor-element elementor-element-24f98ff e-con-full e-flex e-con e-parent e-lazyloaded"
            data-id="24f98ff" data-element_type="container"
            data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
            <div class="elementor-element elementor-element-3241546 e-flex e-con-boxed e-con e-child" data-id="3241546"
                data-element_type="container">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-5ec1ac1 e-con-full e-flex e-con e-child"
                        data-id="5ec1ac1" data-element_type="container">
                        <div class="elementor-element elementor-element-975151c elementor-widget elementor-widget-heading"
                            data-id="975151c" data-element_type="widget" data-widget_type="heading.default">
                            <div class="elementor-widget-container">
                                <div class="elementor-heading-title elementor-size-default">📂</div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-f6c0fc7 elementor-widget elementor-widget-heading"
                            data-id="f6c0fc7" data-element_type="widget" data-widget_type="heading.default">
                            <div class="elementor-widget-container">
                                <h2 class="elementor-heading-title elementor-size-default">Our work</h2>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-5cc2029 elementor-widget elementor-widget-elementskit-heading"
                        data-id="5cc2029" data-element_type="widget" data-widget_type="elementskit-heading.default">
                        <div class="elementor-widget-container">
                            <div class="ekit-wid-con">
                                <div
                                    class="ekit-heading elementskit-section-title-wraper text_left   ekit_heading_tablet-text_center   ekit_heading_mobile-text_center">
                                    <h2 class="ekit-heading--title elementskit-section-title text_fill">
                                        <span><span>Projects, </span></span> we are proud of
                                    </h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="elementor-element elementor-element-e4f2d87 e-con-full e-flex e-con e-child" data-id="e4f2d87" data-element_type="container">
                    <div class="elementor-element elementor-element-e07a7b2 e-con-full e-flex e-con e-child animated slideInUp" data-id="e07a7b2" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;,&quot;animation&quot;:&quot;slideInUp&quot;,&quot;animation_delay&quot;:150}">
                            <div class="elementor-element elementor-element-5404dbb elementor-widget elementor-widget-image" data-id="5404dbb" data-element_type="widget" data-widget_type="image.default">
                            <div class="elementor-widget-container">
                                                                            <a href="#">
                                        <img loading="lazy" decoding="async" width="1024" height="683" src="https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-03.jpg" class="attachment-full size-full wp-image-791" alt="" srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-03.jpg 1024w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-03-300x200.jpg 300w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-03-768x512.jpg 768w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-03-800x534.jpg 800w" sizes="(max-width: 1024px) 100vw, 1024px">								</a>
                                                                        </div>
                            </div>
                    <div class="elementor-element elementor-element-4f7c8bf e-con-full e-flex e-con e-child" data-id="4f7c8bf" data-element_type="container">
                            <div class="elementor-element elementor-element-bc066b9 elementor-widget elementor-widget-heading" data-id="bc066b9" data-element_type="widget" data-widget_type="heading.default">
                            <div class="elementor-widget-container">
                                <h4 class="elementor-heading-title elementor-size-default"><a href="#">Orizon Web development</a></h4>				</div>
                            </div>
                            <div class="elementor-element elementor-element-b8a0fdb elementor-icon-list--layout-inline elementor-list-item-link-full_width elementor-widget elementor-widget-icon-list" data-id="b8a0fdb" data-element_type="widget" data-widget_type="icon-list.default">
                            <div class="elementor-widget-container">
                                        <ul class="elementor-icon-list-items elementor-inline-items">
                                        <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">Web Development,</span>
                                                </li>
                                            <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">Wireframe &amp; Prototyping</span>
                                                </li>
                                    </ul>
                                    </div>
                            </div>
                            </div>
                            </div>
                    <div class="elementor-element elementor-element-88e8303 e-con-full e-flex e-con e-child animated slideInUp" data-id="88e8303" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;,&quot;animation&quot;:&quot;slideInUp&quot;,&quot;animation_delay&quot;:200}">
                            <div class="elementor-element elementor-element-8d2ad88 elementor-widget elementor-widget-image" data-id="8d2ad88" data-element_type="widget" data-widget_type="image.default">
                            <div class="elementor-widget-container">
                                                                            <a href="#">
                                        <img loading="lazy" decoding="async" width="1024" height="683" src="https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-07.jpg" class="attachment-full size-full wp-image-795" alt="" srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-07.jpg 1024w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-07-300x200.jpg 300w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-07-768x512.jpg 768w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-07-800x534.jpg 800w" sizes="(max-width: 1024px) 100vw, 1024px">								</a>
                                                                        </div>
                            </div>
                    <div class="elementor-element elementor-element-15cca69 e-con-full e-flex e-con e-child" data-id="15cca69" data-element_type="container">
                            <div class="elementor-element elementor-element-51b1d3f elementor-widget elementor-widget-heading" data-id="51b1d3f" data-element_type="widget" data-widget_type="heading.default">
                            <div class="elementor-widget-container">
                                <h4 class="elementor-heading-title elementor-size-default"><a href="#">Arno Business Consulting</a></h4>				</div>
                            </div>
                            <div class="elementor-element elementor-element-903e61a elementor-icon-list--layout-inline elementor-list-item-link-full_width elementor-widget elementor-widget-icon-list" data-id="903e61a" data-element_type="widget" data-widget_type="icon-list.default">
                            <div class="elementor-widget-container">
                                        <ul class="elementor-icon-list-items elementor-inline-items">
                                        <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">Web Development,</span>
                                                </li>
                                            <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">Branding, </span>
                                                </li>
                                            <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">Marketing</span>
                                                </li>
                                    </ul>
                                    </div>
                            </div>
                            </div>
                            </div>
                    <div class="elementor-element elementor-element-b5e00d3 e-con-full e-flex e-con e-child animated slideInUp" data-id="b5e00d3" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;,&quot;animation&quot;:&quot;slideInUp&quot;,&quot;animation_delay&quot;:250}">
                            <div class="elementor-element elementor-element-881e125 elementor-widget elementor-widget-image" data-id="881e125" data-element_type="widget" data-widget_type="image.default">
                            <div class="elementor-widget-container">
                                                                            <a href="#">
                                        <img loading="lazy" decoding="async" width="1024" height="683" src="https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-04.jpg" class="attachment-full size-full wp-image-792" alt="" srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-04.jpg 1024w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-04-300x200.jpg 300w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-04-768x512.jpg 768w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-04-800x534.jpg 800w" sizes="(max-width: 1024px) 100vw, 1024px">								</a>
                                                                        </div>
                            </div>
                    <div class="elementor-element elementor-element-aba5dea e-con-full e-flex e-con e-child" data-id="aba5dea" data-element_type="container">
                            <div class="elementor-element elementor-element-5a55a53 elementor-widget elementor-widget-heading" data-id="5a55a53" data-element_type="widget" data-widget_type="heading.default">
                            <div class="elementor-widget-container">
                                <h4 class="elementor-heading-title elementor-size-default"><a href="#">Palet Branding</a></h4>				</div>
                            </div>
                            <div class="elementor-element elementor-element-e49eb02 elementor-icon-list--layout-inline elementor-list-item-link-full_width elementor-widget elementor-widget-icon-list" data-id="e49eb02" data-element_type="widget" data-widget_type="icon-list.default">
                            <div class="elementor-widget-container">
                                        <ul class="elementor-icon-list-items elementor-inline-items">
                                        <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">Branding, </span>
                                                </li>
                                            <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">User Research,</span>
                                                </li>
                                            <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">Corporate Identity</span>
                                                </li>
                                    </ul>
                                    </div>
                            </div>
                            </div>
                            </div>
                    <div class="elementor-element elementor-element-b9002d7 e-con-full e-flex e-con e-child animated slideInUp" data-id="b9002d7" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;,&quot;animation&quot;:&quot;slideInUp&quot;,&quot;animation_delay&quot;:300}">
                            <div class="elementor-element elementor-element-d59ab23 elementor-widget elementor-widget-image" data-id="d59ab23" data-element_type="widget" data-widget_type="image.default">
                            <div class="elementor-widget-container">
                                                                            <a href="#">
                                        <img loading="lazy" decoding="async" width="1024" height="683" src="https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-05.jpg" class="attachment-full size-full wp-image-793" alt="" srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-05.jpg 1024w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-05-300x200.jpg 300w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-05-768x512.jpg 768w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-05-800x534.jpg 800w" sizes="(max-width: 1024px) 100vw, 1024px">								</a>
                                                                        </div>
                            </div>
                    <div class="elementor-element elementor-element-0c454ac e-con-full e-flex e-con e-child" data-id="0c454ac" data-element_type="container">
                            <div class="elementor-element elementor-element-894ba8a elementor-widget elementor-widget-heading" data-id="894ba8a" data-element_type="widget" data-widget_type="heading.default">
                            <div class="elementor-widget-container">
                                <h4 class="elementor-heading-title elementor-size-default"><a href="#">Gista Hosting provider</a></h4>				</div>
                            </div>
                            <div class="elementor-element elementor-element-79ff7bf elementor-icon-list--layout-inline elementor-list-item-link-full_width elementor-widget elementor-widget-icon-list" data-id="79ff7bf" data-element_type="widget" data-widget_type="icon-list.default">
                            <div class="elementor-widget-container">
                                        <ul class="elementor-icon-list-items elementor-inline-items">
                                        <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">Web Development,</span>
                                                </li>
                                            <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">User Interface,</span>
                                                </li>
                                            <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">Web Design</span>
                                                </li>
                                    </ul>
                                    </div>
                            </div>
                            </div>
                            </div>
                    <div class="elementor-element elementor-element-4be58f5 e-con-full e-flex e-con e-child animated slideInUp" data-id="4be58f5" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;,&quot;animation&quot;:&quot;slideInUp&quot;,&quot;animation_delay&quot;:350}">
                            <div class="elementor-element elementor-element-50ca790 elementor-widget elementor-widget-image" data-id="50ca790" data-element_type="widget" data-widget_type="image.default">
                            <div class="elementor-widget-container">
                                                                            <a href="#">
                                        <img loading="lazy" decoding="async" width="1024" height="683" src="https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-02.jpg" class="attachment-full size-full wp-image-790" alt="" srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-02.jpg 1024w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-02-300x200.jpg 300w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-02-768x512.jpg 768w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-02-800x534.jpg 800w" sizes="(max-width: 1024px) 100vw, 1024px">								</a>
                                                                        </div>
                            </div>
                    <div class="elementor-element elementor-element-b7cc9b5 e-con-full e-flex e-con e-child" data-id="b7cc9b5" data-element_type="container">
                            <div class="elementor-element elementor-element-7bfae53 elementor-widget elementor-widget-heading" data-id="7bfae53" data-element_type="widget" data-widget_type="heading.default">
                            <div class="elementor-widget-container">
                                <h2 class="elementor-heading-title elementor-size-default"><a href="#">integra SEO Strategy</a></h2>				</div>
                            </div>
                            <div class="elementor-element elementor-element-376289f elementor-icon-list--layout-inline elementor-list-item-link-full_width elementor-widget elementor-widget-icon-list" data-id="376289f" data-element_type="widget" data-widget_type="icon-list.default">
                            <div class="elementor-widget-container">
                                        <ul class="elementor-icon-list-items elementor-inline-items">
                                        <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">UI &amp; UX design,</span>
                                                </li>
                                            <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">Branding, </span>
                                                </li>
                                            <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">Marketing</span>
                                                </li>
                                    </ul>
                                    </div>
                            </div>
                            </div>
                            </div>
                    <div class="elementor-element elementor-element-a886b0e e-con-full e-flex e-con e-child animated slideInUp" data-id="a886b0e" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;,&quot;animation&quot;:&quot;slideInUp&quot;,&quot;animation_delay&quot;:350}">
                            <div class="elementor-element elementor-element-e43bd74 elementor-widget elementor-widget-image" data-id="e43bd74" data-element_type="widget" data-widget_type="image.default">
                            <div class="elementor-widget-container">
                                                                            <a href="#">
                                        <img loading="lazy" decoding="async" width="1024" height="683" src="https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-01.jpg" class="attachment-full size-full wp-image-789" alt="" srcset="https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-01.jpg 1024w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-01-300x200.jpg 300w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-01-768x512.jpg 768w, https://brandmode.1onestrong.com/wp-content/uploads/2024/03/Porto-01-800x534.jpg 800w" sizes="(max-width: 1024px) 100vw, 1024px">								</a>
                                                                        </div>
                            </div>
                    <div class="elementor-element elementor-element-52dd9a3 e-con-full e-flex e-con e-child" data-id="52dd9a3" data-element_type="container">
                            <div class="elementor-element elementor-element-e158e0a elementor-widget elementor-widget-heading" data-id="e158e0a" data-element_type="widget" data-widget_type="heading.default">
                            <div class="elementor-widget-container">
                                <h2 class="elementor-heading-title elementor-size-default"><a href="#">Counter  Accounting Firm</a></h2>				</div>
                            </div>
                            <div class="elementor-element elementor-element-85fd486 elementor-icon-list--layout-inline elementor-list-item-link-full_width elementor-widget elementor-widget-icon-list" data-id="85fd486" data-element_type="widget" data-widget_type="icon-list.default">
                            <div class="elementor-widget-container">
                                        <ul class="elementor-icon-list-items elementor-inline-items">
                                        <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">Front-End Development,</span>
                                                </li>
                                            <li class="elementor-icon-list-item elementor-inline-item">
                                                    <span class="elementor-icon-list-text">Product Design</span>
                                                </li>
                                    </ul>
                                    </div>
                            </div>
                            </div>
                            </div>
                            </div> 


        </div> -->

            <!-- <div class="elementor-element elementor-element-24f98ff e-con-full e-flex e-con e-parent e-lazyloaded"
            data-id="24f98ff" data-element_type="container"
            data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
            <div class="elementor-element elementor-element-3241546 e-flex e-con-boxed e-con e-child" data-id="3241546"
                data-element_type="container">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-5ec1ac1 e-con-full e-flex e-con e-child"
                        data-id="5ec1ac1" data-element_type="container">
                        <div class="elementor-element elementor-element-975151c elementor-widget elementor-widget-heading"
                            data-id="975151c" data-element_type="widget" data-widget_type="heading.default">
                           
                        </div>
                        <div class="elementor-element elementor-element-f6c0fc7 elementor-widget elementor-widget-heading"
                            data-id="f6c0fc7" data-element_type="widget" data-widget_type="heading.default">
                            
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-5cc2029 elementor-widget elementor-widget-elementskit-heading"
                        data-id="5cc2029" data-element_type="widget" data-widget_type="elementskit-heading.default">
                        <div class="elementor-widget-container">
                            <div class="ekit-wid-con">
                                <div
                                    class="ekit-heading elementskit-section-title-wraper text_left   ekit_heading_tablet-text_center   ekit_heading_mobile-text_center">
                                    <h2 class="ekit-heading--title elementskit-section-title text_fill">
                                        <span><span>Our Proprietary</span></span> AI Solutions,
                                    </h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="max-w-7xl mx-auto">

                <!-- Lead Lifecycle Timeline -->
            <!-- <div class="relative">
                 
                    <div class="flex flex-col md:flex-row items-center mb-24 mobile-timeline-item" id="lead-generation">
                        <div class="w-full md:w-1/2 mb-8 md:mb-0">
                            <div class="glass-card p-8 card-hover h-full">
                                <div class="flex flex-col items-center">
                                    <div class="radar mb-6">
                                        <div class="radar-scan"></div>
                                        <div class="signal-dot" style="top: 30%; left: 60%;"></div>
                                        <div class="signal-dot" style="top: 45%; left: 25%;"></div>
                                        <div class="signal-dot" style="top: 70%; left: 70%;"></div>
                                    </div>
                                    <h3 class="text-2xl font-semibold text-center mb-3">Lead Generation
                                    </h3>
                                    <p class="text-center text-gray-600 mb-6">AI finds your ideal
                                        customers across multiple data sources</p>
                                    <div
                                        class="w-64 h-3 bg-gradient-to-r from-blue-100 via-blue-300 to-cyan-300 rounded-full mb-6">
                                    </div>
                                    <button
                                        class="openModalBtn bg-gradient-to-r from-blue-800 to-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold shadow-lg hover:shadow-xl transition-all hover:scale-105 transform inline-flex items-center"
                                        onclick="window.location.href='contact.html';">
                                        <i class="fas fa-calendar-check mr-2"></i> Request Demo
                                    </button>


                                </div>
                            </div>
                        </div>
                        <div class="w-full md:w-1/2 md:pl-12 timeline-connector">
                            <h2 class="text-3xl font-bold mb-4">1. AI-Powered Lead Discovery</h2>
                            <p class="text-lg text-black-300 mb-6">Our system scans millions of data
                                points to identify your perfect customers based on behavior patterns,
                                intent signals, and ideal customer profiles.</p>
                            <div class="bg-white/50 p-4 rounded-xl mb-4">
                                <div class="text-sm font-mono text-gray-200 overflow-hidden">
                                    <div class="typing-indicator">
                                        <span class="typing-dot"></span>
                                        <span class="typing-dot"></span>
                                        <span class="typing-dot"></span>
                                    </div>
                                    <div class="pl-4">
                                        >> Scanning LinkedIn, Crunchbase, Google<br>
                                        >> Analyzing 1,243 matching companies<br>
                                        >> Verified 379 decision-maker contacts<br>
                                        >> Loading into automation sequence...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                  
                    <div class="flex flex-col md:flex-row items-center mb-24 mobile-timeline-item" id="cold-outreach">
                        <div class="w-full md:w-1/2 order-1 md:order-2 mb-8 md:mb-0">
                            <div class="glass-card p-8 card-hover h-full">
                                <div class="flex flex-col items-center">
                                    <div class="flex space-x-4 mb-6">
                                        <div class="bg-white/90 p-4 rounded-xl shadow-md">
                                            <i class="fas fa-envelope text-3xl text-blue-500"></i>
                                        </div>
                                        <div class="bg-white/90 p-4 rounded-xl shadow-md">
                                            <i class="fas fa-comment-dots text-3xl text-green-500"></i>
                                        </div>
                                        <div class="bg-white/90 p-4 rounded-xl shadow-md">
                                            <i class="fab fa-linkedin text-3xl text-blue-600"></i>
                                        </div>
                                    </div>
                                    <div class="space-y-2 w-full mb-6">
                                        <div class="message-bubble bg-blue-500 text-white ml-auto rounded-tl-xl rounded-bl-xl rounded-br-xl"
                                            style="animation-delay: 0.2s;">
                                            Hi [First Name], noticed your recent [trigger event]...
                                        </div>
                                        <div class="message-bubble bg-gray-200 text-black-300 rounded-tr-xl rounded-bl-xl rounded-br-xl"
                                            style="animation-delay: 0.4s;">
                                            Thanks for reaching out! Can you send more info?
                                        </div>
                                        <div class="message-bubble bg-blue-500 text-white ml-auto rounded-tl-xl rounded-bl-xl rounded-br-xl"
                                            style="animation-delay: 0.6s;">
                                            Absolutely! Here's our case study →
                                        </div>
                                    </div>
                                    <button
                                        class="openModalBtn bg-gradient-to-r from-blue-800 to-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold shadow-lg hover:shadow-xl transition-all hover:scale-105 transform inline-flex items-center"
                                        onclick="window.location.href='contact.html';">
                                        <i class="fas fa-calendar-check mr-2"></i> Request Demo
                                    </button>


                                </div>
                            </div>
                        </div>
                        <div class="w-full md:w-1/2 order-2 md:order-1 md:pr-12 timeline-connector">
                            <h2 class="text-3xl font-bold mb-4">2. Multi-Channel Outreach</h2>
                            <p class="text-lg text-gray-300 mb-6">Personalized messages across email,
                                SMS, and social channels - with AI optimizing timing, messaging, and
                                follow-ups based on engagement.</p>
                            <div class="space-y-2">
                                <div class="flex items-center bg-white/50 p-3 rounded-lg">
                                    <div
                                        class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-check text-green-200"></i>
                                    </div>
                                    <span class="text-gray-300">87% open rate compared to industry
                                        27%</span>
                                </div>
                                <div class="flex items-center bg-white/50 p-3 rounded-lg">
                                    <div
                                        class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-bolt text-yellow-500"></i>
                                    </div>
                                    <span class="text-gray-300">Automatic response handling for 24/7
                                        coverage</span>
                                </div>
                            </div>
                        </div>
                    </div>

                 
                    <div class="flex flex-col md:flex-row items-center mb-24 mobile-timeline-item" id="lead-conversion">
                        <div class="w-full md:w-1/2 mb-8 md:mb-0">
                            <div class="glass-card p-8 card-hover h-full">
                                <div class="flex flex-col items-center">
                                    <div class="relative mb-6">
                                        <div class="w-48 h-32 bg-white/90 rounded-xl shadow-md p-4">
                                            <div class="flex justify-between mb-2">
                                                <div class="text-sm font-medium">Incoming Leads</div>
                                                <div class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                                                    +12 today</div>
                                            </div>
                                            <div class="h-16 bg-blue-50 rounded-lg p-2 mb-1 flex items-center">
                                                <div
                                                    class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                                                    <i class="fas fa-user text-blue-500 text-sm"></i>
                                                </div>
                                                <div class="text-sm">
                                                    <div class="font-medium">Sarah Johnson</div>
                                                    <div class="text-xs text-gray-500">VP Marketing
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="absolute -right-4 -top-4 bg-blue-500 text-white p-2 rounded-full text-xs font-bold">
                                            LIVE
                                        </div>
                                    </div>
                                    <div class="countdown mb-4">00:09.4 response time</div>
                                    <h3 class="text-2xl font-semibold text-center mb-3">Instant
                                        Engagement</h3>
                                    <p class="text-center text-gray-600 mb-6">AI responds faster than
                                        your competition can</p>
                                    <button
                                        class="openModalBtn bg-gradient-to-r from-blue-800 to-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold shadow-lg hover:shadow-xl transition-all hover:scale-105 transform inline-flex items-center"
                                        onclick="window.location.href='contact.html';">
                                        <i class="fas fa-calendar-check mr-2"></i> Request Demo
                                    </button>


                                </div>
                            </div>
                        </div>
                        <div class="w-full md:w-1/2 md:pl-12 timeline-connector">
                            <h2 class="text-3xl font-bold mb-4">3. AI Speed to Lead</h2>
                            <p class="text-lg text-gray-300 mb-6">The moment a lead engages, our system
                                responds instantly - dramatically increasing conversion rates compared
                                to manual follow-up.</p>
                            <div class="bg-blue-50/50 p-4 rounded-xl mb-4">
                                <div class="flex justify-between items-center mb-2">
                                    <div class="font-medium text-gray-300">Typical Manual Response:
                                    </div>
                                    <div class="bg-red-100 text-red-700 px-2 py-1 rounded text-sm">47
                                        hours</div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div class="font-medium text-gray-300">Bella AI Response:</div>
                                    <div class="bg-green-100 text-green-700 px-2 py-1 rounded text-sm">
                                        &lt; 10 seconds</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-300">
                                <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                Leads are 21x more likely to convert when contacted within 5 minutes vs.
                                30 minutes.
                            </div>
                        </div>
                    </div>

               
                    <div class="flex flex-col md:flex-row items-center mb-24 mobile-timeline-item"
                        id="database-reactivation">
                        <div class="w-full md:w-1/2 order-1 md:order-2 mb-8 md:mb-0">
                            <div class="glass-card p-8 card-hover h-full">
                                <div class="flex flex-col items-center">
                                    <div class="relative mb-6">
                                        <div
                                            class="w-32 h-32 bg-gray-100 rounded-xl shadow-md p-4 text-center flex flex-col items-center justify-center reactivating">
                                            <i class="fas fa-database text-4xl text-gray-400 mb-2"></i>
                                            <span class="text-sm font-medium">Old Leads</span>
                                            <span class="text-xs text-gray-500">23 dormant
                                                accounts</span>
                                        </div>
                                        <div
                                            class="absolute -right-2 -top-2 w-12 h-12 bg-blue-500 text-white rounded-full flex items-center justify-center animate-pulse">
                                            <i class="fas fa-bolt"></i>
                                        </div>
                                    </div>
                                    <h3 class="text-2xl font-semibold text-center mb-3">Dormant Lead
                                        Revival</h3>
                                    <p class="text-center text-gray-600 mb-6">Automatically re-engage
                                        cold contacts when conditions change</p>
                                    <button
                                        class="openModalBtn bg-gradient-to-r from-blue-800 to-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold shadow-lg hover:shadow-xl transition-all hover:scale-105 transform inline-flex items-center"
                                        onclick="window.location.href='contact.html';">
                                        <i class="fas fa-calendar-check mr-2"></i> Request Demo
                                    </button>


                                </div>
                            </div>
                        </div>
                        <div class="w-full md:w-1/2 order-2 md:order-1 md:pr-12 timeline-connector">
                            <h2 class="text-3xl font-bold mb-4">4. Smart Reactivation</h2>
                            <p class="text-lg text-gray-300 mb-6">Our system continuously monitors for
                                trigger events to revive old leads. When a company raises funding, hires
                                new executives, or shows buying signals - we re-engage.</p>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div
                                        class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-1">
                                        <i class="fas fa-calendar-check text-blue-500 text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium">Trigger-Based Followup</div>
                                        <div class="text-sm text-gray-300">When conditions change, AI
                                            initiates outreach with relevant context</div>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div
                                        class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-1">
                                        <i class="fas fa-chart-line text-green-500 text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium">32% Conversion Rate</div>
                                        <div class="text-sm text-gray-300">Reactivated leads convert at
                                            higher rates than cold prospects</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col md:flex-row items-center mb-24 mobile-timeline-item"
                        id="appointment-setting">
                        <div class="w-full md:w-1/2 mb-8 md:mb-0">
                            <div class="glass-card p-8 card-hover h-full">
                                <div class="flex flex-col items-center">
                                    <div class="relative mb-6">
                                        <div class="w-48 h-48 bg-white/90 rounded-xl shadow-md p-4">
                                            <div class="text-center font-medium mb-3">Calendar Booking
                                            </div>
                                            <div class="grid grid-cols-7 gap-1 mb-2 text-xs">
                                                <div>S</div>
                                                <div>M</div>
                                                <div>T</div>
                                                <div>W</div>
                                                <div>T</div>
                                                <div>F</div>
                                                <div>S</div>
                                            </div>
                                            <div class="grid grid-cols-7 gap-1 text-xs">
                                                <div></div>
                                                <div></div>
                                                <div>1</div>
                                                <div>2</div>
                                                <div>3</div>
                                                <div>4</div>
                                                <div>5</div>
                                                <div>6</div>
                                                <div
                                                    class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
                                                    7</div>
                                                <div>8</div>
                                                <div>9</div>
                                                <div>10</div>
                                                <div>11</div>
                                                <div>12</div>
                                            </div>
                                            <div class="mt-4 space-y-2">
                                                <div class="bg-blue-50 rounded-lg p-2 text-xs flex items-center">
                                                    <div class="w-2 h-2 bg-blue-500 rounded-full mr-2">
                                                    </div>
                                                    <span>2:00 PM - Product Demo</span>
                                                </div>
                                                <div class="bg-green-50 rounded-lg p-2 text-xs flex items-center">
                                                    <div class="w-2 h-2 bg-green-500 rounded-full mr-2">
                                                    </div>
                                                    <span>3:30 PM - Discovery Call</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="absolute -right-4 -top-4">
                                            <div class="bg-white p-2 rounded-full shadow-lg flex">
                                                <i class="fas fa-envelope text-blue-500 mx-1"></i>
                                                <i class="fas fa-sms text-green-500 mx-1"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        class="openModalBtn bg-gradient-to-r from-blue-800 to-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold shadow-lg hover:shadow-xl transition-all hover:scale-105 transform inline-flex items-center"
                                        onclick="window.location.href='contact.html';">
                                        <i class="fas fa-calendar-check mr-2"></i> Request Demo
                                    </button>


                                </div>
                            </div>
                        </div>
                        <div class="w-full md:w-1/2 md:pl-12 timeline-connector">
                            <h2 class="text-3xl font-bold mb-4">5. Automated Scheduling</h2>
                            <p class="text-lg text-gray-300 mb-6">AI handles all the back-and-forth of
                                finding meeting times that work for both parties, with automated
                                reminders to reduce no-shows.</p>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-magic text-blue-500 text-sm"></i>
                                    </div>
                                    <span>Smart timezone detection</span>
                                </div>
                                <div class="flex items-center">
                                    <div
                                        class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-sync-alt text-green-500 text-sm"></i>
                                    </div>
                                    <span>Rescheduling handled automatically</span>
                                </div>
                                <div class="flex items-center">
                                    <div
                                        class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-bell text-purple-500 text-sm"></i>
                                    </div>
                                    <span>24h & 1h reminder sequences</span>
                                </div>
                            </div>
                        </div>
                    </div>

                  
                    <div class="flex flex-col md:flex-row items-center mb-24 mobile-timeline-item" id="voice-ai">
                        <div class="w-full md:w-1/2 order-1 md:order-2 mb-8 md:mb-0">
                            <div class="glass-card p-8 card-hover h-full">
                                <div class="flex flex-col items-center">
                                    <div class="phone-ring mb-6">
                                        <div class="w-24 h-44 bg-white rounded-3xl shadow-xl p-4 relative">
                                            <div
                                                class="absolute top-2 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-gray-200 rounded-full">
                                            </div>
                                            <div class="flex flex-col items-center mt-6">
                                                <div
                                                    class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                                                    <i class="fas fa-headset text-blue-500 text-xl"></i>
                                                </div>
                                                <div class="text-sm font-medium mb-2 text-center">Bella
                                                    AI Assistant</div>
                                                <div class="w-16 h-4 bg-gray-100 rounded-full mb-4">
                                                </div>
                                                <div class="relative w-16 h-8">
                                                    <div class="absolute inset-0 bg-blue-500 rounded-full">
                                                    </div>
                                                    <div
                                                        class="absolute inset-1 bg-white rounded-full flex items-center justify-center">
                                                        <i class="fas fa-phone-alt text-blue-500"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <h3 class="text-2xl font-semibold text-center mb-3">Voice AI
                                        Receptionist</h3>
                                    <p class="text-center text-gray-600 mb-6">Natural conversations that
                                        qualify leads 24/7</p>

                                    <div class="flex justify-center items-center gap-6">
                                       
                                        <a href="tel:+16467626240"
                                            style="position: relative; display: inline-block; text-decoration: none;">
                                           
                                            <div
                                                style="color: black; background-color: rgba(239, 246, 255, 0.5); border-radius: 25px; max-width: 200px; padding: 12px 50px 12px 20px; font-size: 16px; font-weight: 500; text-align: center; border: none; cursor: pointer; pointer-events: auto;">
                                                ****** 762 6240
                                            </div>

                                        
                                            <div
                                                style="position: absolute; top: 50%; right: 8px; transform: translateY(-50%); background: linear-gradient(to right, #1e40af, #2563eb); width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; box-shadow: 0 2px 10px rgba(0,0,0,0.25);">
                                                <i class="fas fa-phone-alt"></i>
                                            </div>
                                        </a>
                                    </div>



                                </div>
                            </div>
                        </div>
                        <div class="w-full md:w-1/2 order-2 md:order-1 md:pr-12 timeline-connector">
                            <h2 class="text-3xl font-bold mb-4">6. AI Voice Conversations</h2>
                            <p class="text-lg text-gray-300 mb-6">Our AI assistant answers calls with
                                human-like conversations to qualify leads, schedule meetings, and answer
                                common questions.</p>
                            <div class="bg-blue-50/50 p-4 rounded-xl mb-4">
                                <div class="flex items-start mb-2">
                                    <div
                                        class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-1">
                                        <i class="fas fa-quote-left text-blue-500 text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium">Sample Conversation:</div>
                                        <div class="text-sm text-gray-200 italic">
                                            "Hi this is Sarah with Acme Co, I had some questions about
                                            your platform?"
                                        </div>
                                        <div class="text-sm text-gray-200 italic ml-4 mt-1">
                                            "Hello Sarah! I can help with that. Which features are you
                                            most interested in learning about?"
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600">
                                <i class="fas fa-chart-pie text-blue-500 mr-2"></i>
                                74% of callers can't distinguish our AI from human agents.
                            </div>
                        </div>
                    </div>

                 
                    <div class="flex flex-col md:flex-row items-center mb-24 mobile-timeline-item" id="chat-widget">
                        <div class="w-full md:w-1/2 mb-8 md:mb-0">
                            <div class="glass-card p-8 card-hover h-full">
                                <div class="flex flex-col items-center">
                                    <div class="w-64 bg-white rounded-xl shadow-lg overflow-hidden mb-6"
                                        style="max-height: 350px; min-height: 350px">
                                        <div class="bg-gradient-to-r from-blue-500 to-cyan-500 p-3 text-white">
                                            <div class="flex items-center">
                                                <div
                                                    class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-2">
                                                    <i class="fas fa-robot"></i>
                                                </div>
                                                <div>Bella AI Assistant</div>
                                            </div>
                                        </div>
                                        <div class="p-3 h-40 overflow-y-auto"
                                            style="max-height: 300px; min-height: 300px">
                                            <div class="mb-2">
                                                <div class="bg-gray-100 rounded-lg p-2 text-sm inline-block">
                                                    Hi there! How can I help you today?
                                                </div>
                                            </div>
                                            <div class="mb-2">
                                                <div class="bg-gray-100 rounded-lg p-2 text-sm inline-block">
                                                    Here are some quick options:
                                                </div>
                                            </div>
                                            <div class="flex space-x-2 mb-2">
                                                <button
                                                    class="bg-blue-50 hover:bg-blue-100 text-blue-600 text-xs px-3 py-1 rounded-full">
                                                    Pricing
                                                </button>
                                                <button
                                                    class="bg-blue-50 hover:bg-blue-100 text-blue-600 text-xs px-3 py-1 rounded-full">
                                                    Demo
                                                </button>
                                            </div>
                                            <div class="flex justify-center">
                                                <div class="typing-indicator bg-gray-100 p-2 rounded-full">
                                                    <span class="typing-dot"></span>
                                                    <span class="typing-dot"></span>
                                                    <span class="typing-dot"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        class="openModalBtn bg-gradient-to-r from-blue-800 to-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold shadow-lg hover:shadow-xl transition-all hover:scale-105 transform inline-flex items-center"
                                        onclick="window.location.href='contact.html';">
                                        <i class="fas fa-calendar-check mr-2"></i> Request Demo
                                    </button>


                                </div>
                            </div>
                        </div>
                        <div class="w-full md:w-1/2 md:pl-12 timeline-connector">
                            <h2 class="text-3xl font-bold mb-4">7. Conversational AI Chat</h2>
                            <p class="text-lg text-gray-300 mb-6">Website visitors get instant answers
                                from our AI chat that understands context and qualifies leads while your
                                team sleeps.</p>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div
                                        class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3 mt-1">
                                        <i class="fas fa-comments text-purple-500 text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium">Natural Language Understanding</div>
                                        <div class="text-sm text-gray-600">Answers complex questions
                                            with relevant responses</div>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div
                                        class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-1">
                                        <i class="fas fa-tasks text-green-500 text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium">Lead Qualification</div>
                                        <div class="text-sm text-gray-600">Captures key details and
                                            scores leads for followup</div>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div
                                        class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3 mt-1">
                                        <i class="fas fa-calendar-check text-yellow-500 text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium">Meeting Scheduling</div>
                                        <div class="text-sm text-gray-600">Directly books qualified
                                            leads on your calendar</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

              
                    <div class="flex flex-col md:flex-row items-center mb-16 mobile-timeline-item" id="abandoned-cart">
                        <div class="w-full md:w-1/2 order-1 md:order-2 mb-8 md:mb-0">
                            <div class="glass-card p-8 card-hover h-full">
                                <div class="flex flex-col items-center">
                                    <div class="relative mb-6">
                                        <div class="w-56 bg-white rounded-lg shadow-md p-4">
                                            <div class="flex justify-between items-center mb-3">
                                                <h4 class="font-medium" style="color: black">Cart
                                                    Recovery Sequence</h4>
                                                <div class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">
                                                    Active</div>
                                            </div>
                                            <div class="flex items-center space-x-3 mb-3">
                                                <div
                                                    class="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center">
                                                    <i class="fas fa-shopping-cart text-blue-400"></i>
                                                </div>
                                                <div class="flex-1">
                                                    <div class="font-medium text-sm">Your cart is
                                                        waiting!</div>
                                                    <div class="text-xs text-gray-500">2 items • $247
                                                        total</div>
                                                </div>
                                            </div>
                                            <div class="text-xs text-gray-600 mb-4">
                                                5 customers recovered today ($1,235)
                                            </div>
                                            <div class="flex">
                                                <button
                                                    class="flex-1 bg-blue-500 hover:bg-blue-600 text-white text-sm py-2 rounded-l-lg">
                                                    Send Offer
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <button
                                        class="openModalBtn bg-gradient-to-r from-blue-800 to-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold shadow-lg hover:shadow-xl transition-all hover:scale-105 transform inline-flex items-center"
                                        onclick="window.location.href='contact.html';">
                                        <i class="fas fa-calendar-check mr-2"></i> Request Demo
                                    </button>


                                </div>
                            </div>
                        </div>
                        <div class="w-full md:w-1/2 order-2 md:order-1 md:pr-12">
                            <h2 class="text-3xl font-bold mb-4">8. Lost Opportunity Recovery</h2>
                            <p class="text-lg text-gray-300 mb-6">Our system detects when leads drop off
                                and automatically follows up with tailored messages, special offers, and
                                alternative approaches.</p>
                            <div class="bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-xl">
                                <div class="font-medium mb-2">Typical Recovery Workflow:</div>
                                <ol class="text-sm list-decimal list-inside space-y-1 text-black">
                                    <li>1h after: Friendly reminder with benefit recap</li>
                                    <li>24h after: Customer success story + social proof</li>
                                    <li>48h after: Limited-time offer (if applicable)</li>
                                    <li>72h after: Alternative solution or downsell</li>
                                </ol>
                            </div>
                        </div>
                    </div> 
                </div> -->

            <!-- Final CTA -->
            <!-- <div class="glass-card p-8 md:p-12 rounded-2xl mt-16 mb-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">What Else Can Bella AI Do For You?</h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-8">Complete the picture with our full suite of AI-powered business automation tools.</p>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-10">
                <div class="bg-white rounded-xl shadow-lg p-6 transition-all duration-300 hover:shadow-xl">
                    <h2 class="text-2xl font-semibold text-black mb-6">Adjust Your Metrics</h2>
                    
                    <div class="mb-6">
                        <div class="flex justify-between mb-2">
                            <label for="monthlyLeads" class="font-medium text-black">Monthly Leads</label>
                            <span id="monthlyLeadsValue" class="font-bold text-black">100</span>
                        </div>
                        <input type="range" id="monthlyLeads" min="10" max="1000" value="100" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>10</span>
                            <span>1000</span>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <div class="flex justify-between mb-2">
                            <label for="conversionRate" class="font-medium text-gray-700">Conversion Rate</label>
                            <span id="conversionRateValue" class="font-bold text-blue-600">2.5%</span>
                        </div>
                        <input type="range" id="conversionRate" min="0.5" max="10" step="0.1" value="2.5" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>0.5%</span>
                            <span>10%</span>
                        </div>
                    </div>
                    
                    <div class="mb-8">
                        <div class="flex justify-between mb-2">
                            <label for="dealSize" class="font-medium text-gray-700">Average Deal Size</label>
                            <span id="dealSizeValue" class="font-bold text-blue-600">$5,000</span>
                        </div>
                        <input type="range" id="dealSize" min="1000" max="100000" step="1000" value="5000" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>$1K</span>
                            <span>$100K</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-center">
                        <button id="resetBtn" class="px-6 py-2 bg-gray-200 text-gray-700 rounded-lg font-medium hover:bg-gray-300 transition-colors">
                            Reset Values
                        </button>
                    </div>
                </div>
                
                <div class="space-y-6">
                    <div class="bg-white rounded-xl shadow-lg p-6 transition-all duration-300 hover:shadow-xl card-tilt">
                        <h3 class="text-lg font-semibold text-gray-700 mb-2">Current Performance</h3>
                        <div class="progress-container mb-3">
                            <div id="currentProgress" class="progress-bar bg-blue-400" style="width: 0%"></div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm text-gray-600">Monthly Revenue</p>
                                <p id="currentRevenue" class="text-2xl font-bold text-black-300">$12,500</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Deals Closed</p>
                                <p id="currentDeals" class="text-2xl font-bold text-black-300">2.5</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-lg p-6 transition-all duration-300 hover:shadow-xl card-tilt">
                        <h3 class="text-lg font-semibold text-gray-700 mb-2">Projected Performance</h3>
                        <div class="progress-container mb-3">
                            <div id="projectedProgress" class="progress-bar bg-green-500" style="width: 0%"></div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm text-gray-600">Monthly Revenue</p>
                                <p id="projectedRevenue" class="text-2xl font-bold text-black-300">$36,250</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Deals Closed</p>
                                <p id="projectedDeals" class="text-2xl font-bold text-black-300">7.25</p>
                            </div>
                        </div>
                    </div>
                    
                    <div id="roiCard" class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500 pulse-animate">
                        <h3 class="text-lg font-semibold text-gray-700 mb-1">Revenue Impact</h3>
                        <div class="flex items-end">
                            <span id="roiPercentage" class="text-4xl font-bold text-green-600 mr-2">+190%</span>
                            <p class="text-sm text-gray-600 mb-2">increase in monthly revenue</p>
                        </div>
                        <div class="mt-3">
                            <p class="text-sm text-gray-600">Annual Revenue Increase:</p>
                            <p id="annualRevenue" class="text-xl font-bold text-black-300">$285,000</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-lg p-6 mt-6">
                <h2 class="text-2xl font-semibold text-black mb-4">Performance Insights</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-blue-300 p-4 rounded-lg">
                        <h3 class="font-medium text-gray-700 mb-2">Lead Efficiency</h3>
                        <div class="text-3xl font-bold text-blue-600">
                            <span id="leadsPerDeal">40</span>
                            <span class="text-sm">leads/deal</span>
                        </div>
                    </div>
                    <div class="bg-indigo-300 p-4 rounded-lg">
                        <h3 class="font-medium text-gray-700 mb-2">Revenue per Lead</h3>
                        <div class="text-3xl font-bold text-indigo-600">
                            $<span id="revenuePerLead">125</span>
                        </div>
                    </div>
                    <div class="bg-emerald-300 p-4 rounded-lg">
                        <h3 class="font-medium text-gray-100 mb-2">Close Rate Needed</h3>
                        <div class="text-3xl font-bold text-emerald-600">
                            <span id="closeRateNeeded">7.25</span>
                            <span class="text-sm">deals/month</span>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 p-4 bg-yellow-200 rounded-lg border-l-4 border-yellow-400">
                    <h3 class="font-medium text-gray-700 mb-2">Recommendation</h3>
                    <p id="recommendation" class="text-gray-700">Based on your current metrics, focus on improving your conversion rate first, as it currently requires 40 leads to generate one deal. A small increase in conversion rate would have a significant impact on your revenue.</p>
                </div>
            </div>
        </div> 
            </div>
        </div> -->

            <!-- <div class="elementor-element elementor-element-1485941 e-con-full e-flex e-con e-parent" data-id="1485941"
            data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;gradient&quot;}">
            <div class="elementor-element elementor-element-5b01882 e-flex e-con-boxed e-con e-child" data-id="5b01882"
                data-element_type="container">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-9ae89b6 elementor-widget__width-initial elementor-widget elementor-widget-elementskit-heading"
                        data-id="9ae89b6" data-element_type="widget" data-widget_type="elementskit-heading.default">
                        <div class="elementor-widget-container">
                            <div class="ekit-wid-con">
                                <div
                                    class="ekit-heading elementskit-section-title-wraper text_left   ekit_heading_tablet-   ekit_heading_mobile-text_center">
                                    <h2 class="ekit-heading--title elementskit-section-title text_fill">
                                        Transform your
                                        <span><span>brand today!</span></span>
                                    </h2>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-569ec39 elementor-align-left elementor-widget elementor-widget-elementskit-button"
                        data-id="569ec39" data-element_type="widget" data-widget_type="elementskit-button.default">
                        <div class="elementor-widget-container">
                            <div class="ekit-wid-con">
                                <div class="ekit-btn-wraper">
                                    <a href="/contact.html" class="elementskit-btn  whitespace--normal" id="">
                                        Let&#039;s Discuss a project<i class="icon icon-right-arrow1"></i> </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> -->


        </div>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
            /* Custom scrollbar */
            .benefits-container::-webkit-scrollbar {
                height: 8px;
            }

            .benefits-container::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
            }

            .benefits-container::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 10px;
            }

            .benefits-container::-webkit-scrollbar-thumb:hover {
                background: rgba(255, 255, 255, 0.5);
            }

            /* Hide scrollbar but keep functionality */
            .benefits-container {
                -ms-overflow-style: none;
                /* IE and Edge */
                scrollbar-width: none;
                /* Firefox */
            }

            .benefits-container::-webkit-scrollbar {
                display: none;
                /* Chrome, Safari and Opera */
            }

            /* Smooth hover effect for cards */
            .benefit-card {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }

            .benefit-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
            }

            /* Navigation buttons */
            .nav-button {
                opacity: 0.6;
                transition: opacity 0.3s ease, transform 0.3s ease;
            }

            .nav-button:hover {
                opacity: 1;
                transform: scale(1.1);
            }
        </style>
        <div class="container mx-auto px-4 py-20">
            <!-- Header Section -->
            <div class="max-w-7xl mx-auto text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-4">
                    The Benefits of Partnering with Us
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Experience transformative growth with our cutting-edge AI solutions designed to optimize every
                    aspect of your business.
                </p>
            </div>
            <!-- Benefits Cards Container -->
            <div class="relative">
                <div class="benefits-container flex overflow-x-auto space-x-6 py-4 px-2" id="benefitsContainer"
                    style="scroll-behavior: smooth;">
                    <!-- Advanced AI -->
                    <div
                        class="benefit-card flex-shrink-0 w-80 md:w-96 bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-700">
                        <div
                            class="w-16 h-16 bg-indigo-600 rounded-lg flex items-center justify-center mb-6 text-white">
                            <i class="fas fa-brain text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">Advanced AI</h3>
                        <p class="text-gray-300">AI is changing at a rapid pace and it's easy to get left behind. We
                            stay ahead and simplify everything so you're always at the forefront. Our systems
                            continuously improve to ensure we provide the most advanced solutions.</p>
                    </div>

                    <!-- Reclaimed Time -->
                    <div
                        class="benefit-card flex-shrink-0 w-80 md:w-96 bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-700">
                        <div class="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mb-6 text-white">
                            <i class="fas fa-clock text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">Reclaimed Time</h3>
                        <p class="text-gray-300">With AI-powered systems and intelligent automation, your business can
                            eliminate manual processes. Free up your team to focus on what truly drives growth—serving
                            clients and closing new deals.</p>
                    </div>

                    <!-- Increased Revenue -->
                    <div
                        class="benefit-card flex-shrink-0 w-80 md:w-96 bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-700">
                        <div class="w-16 h-16 bg-green-600 rounded-lg flex items-center justify-center mb-6 text-white">
                            <i class="fas fa-chart-line text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">Increased Revenue</h3>
                        <p class="text-gray-300">Our proven system guarantees consistent and predictable results,
                            empowering you to scale your business to new levels. We'll help you maximize earnings and
                            profit as much as possible.</p>
                    </div>

                    <!-- Elevated Profit Margins -->
                    <div
                        class="benefit-card flex-shrink-0 w-80 md:w-96 bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-700">
                        <div
                            class="w-16 h-16 bg-purple-600 rounded-lg flex items-center justify-center mb-6 text-white">
                            <i class="fas fa-coins text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">Elevated Profit Margins</h3>
                        <p class="text-gray-300">Replace costly outdated processes with efficient advanced alternatives.
                            Our solutions run your sales process autonomously and drastically reduce your company's
                            overhead.</p>
                    </div>

                    <!-- Higher Valuation -->
                    <div
                        class="benefit-card flex-shrink-0 w-80 md:w-96 bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-700">
                        <div
                            class="w-16 h-16 bg-yellow-600 rounded-lg flex items-center justify-center mb-6 text-white">
                            <i class="fas fa-briefcase text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">Higher Valuation</h3>
                        <p class="text-gray-300">Instantly own a more valuable business with streamlined processes, more
                            revenue, and less overhead. We'll show you how to utilize proprietary technology to ensure
                            continuous growth.</p>
                    </div>

                    <!-- Guaranteed Results -->
                    <div
                        class="benefit-card flex-shrink-0 w-80 md:w-96 bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-700">
                        <div class="w-16 h-16 bg-red-600 rounded-lg flex items-center justify-center mb-6 text-white">
                            <i class="fas fa-medal text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">Guaranteed Results</h3>
                        <p class="text-gray-300">We guarantee a return on our partner's investments. As our client, we
                            ensure complete satisfaction and work relentlessly to achieve your business goals.</p>
                    </div>

                    <!-- Top-Tier Partnerships -->
                    <div
                        class="benefit-card flex-shrink-0 w-80 md:w-96 bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-700">
                        <div class="w-16 h-16 bg-teal-600 rounded-lg flex items-center justify-center mb-6 text-white">
                            <i class="fas fa-handshake text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">Top-Tier Partnerships</h3>
                        <p class="text-gray-300">Many of our clients are ideal referral partners for one another.
                            Becoming our client grants you access to our network of high-performing companies and
                            business owners.</p>
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <button id="prevBtn"
                    class="nav-button absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 bg-gray-800 text-white w-10 h-10 rounded-full shadow-lg flex items-center justify-center">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button id="nextBtn"
                    class="nav-button absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 bg-gray-800 text-white w-10 h-10 rounded-full shadow-lg flex items-center justify-center">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <!-- Mobile Indicator -->
            <div class="flex justify-center mt-6 md:hidden">
                <div class="flex space-x-2">
                    <div class="w-2 h-2 rounded-full bg-gray-600"></div>
                    <div class="w-2 h-2 rounded-full bg-gray-600"></div>
                    <div class="w-2 h-2 rounded-full bg-gray-400"></div>
                    <div class="w-2 h-2 rounded-full bg-gray-600"></div>
                    <div class="w-2 h-2 rounded-full bg-gray-600"></div>
                    <div class="w-2 h-2 rounded-full bg-gray-600"></div>
                    <div class="w-2 h-2 rounded-full bg-gray-600"></div>
                </div>
            </div>
        </div>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                const container = document.getElementById('benefitsContainer');
                const prevBtn = document.getElementById('prevBtn');
                const nextBtn = document.getElementById('nextBtn');

                // Card width + gap (24px in this case)
                const scrollAmount = 384; // 80 (card width) + 24 (gap) = 104 * 3.69 for proper scrolling

                nextBtn.addEventListener('click', function () {
                    container.scrollBy({
                        left: scrollAmount,
                        behavior: 'smooth'
                    });
                });

                prevBtn.addEventListener('click', function () {
                    container.scrollBy({
                        left: -scrollAmount,
                        behavior: 'smooth'
                    });
                });

                // Hide/show buttons based on scroll position
                container.addEventListener('scroll', function () {
                    const maxScrollLeft = container.scrollWidth - container.clientWidth;

                    if (container.scrollLeft <= 0) {
                        prevBtn.style.opacity = '0.2';
                        prevBtn.style.pointerEvents = 'none';
                    } else {
                        prevBtn.style.opacity = '0.7';
                        prevBtn.style.pointerEvents = 'auto';
                    }

                    if (container.scrollLeft >= maxScrollLeft - 5) {
                        nextBtn.style.opacity = '0.2';
                        nextBtn.style.pointerEvents = 'none';
                    } else {
                        nextBtn.style.opacity = '0.7';
                        nextBtn.style.pointerEvents = 'auto';
                    }
                });

                // Initialize button states
                prevBtn.style.opacity = '0.2';
                prevBtn.style.pointerEvents = 'none';
            });
        </script>

        <div data-elementor-type="footer" data-elementor-id="1764"
            class="elementor elementor-1764 elementor-location-footer" data-elementor-post-type="elementor_library">
            <div class="elementor-element elementor-element-78cad96 e-flex e-con-boxed e-con e-parent" data-id="78cad96"
                data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-52abb05 e-con-full e-flex e-con e-child"
                        data-id="52abb05" data-element_type="container">
                        <div class="elementor-element elementor-element-600cf77 elementor-widget elementor-widget-elementskit-heading"
                            data-id="600cf77" data-element_type="widget" data-widget_type="elementskit-heading.default">
                            <div class="elementor-widget-container">
                                <div class="ekit-wid-con">
                                    <div
                                        class="ekit-heading elementskit-section-title-wraper text_left   ekit_heading_tablet-text_center   ekit_heading_mobile-text_left">
                                        <h3 class="ekit-heading--title elementskit-section-title text_fill">
                                            Ready to transform <span><span>your
                                                    business? </span></span></h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-8d50095 elementor-align-left elementor-align--tabletcenter elementor-widget elementor-widget-elementskit-button"
                            data-id="8d50095" data-element_type="widget" data-widget_type="elementskit-button.default">
                            <div class="elementor-widget-container">
                                <div class="ekit-wid-con">
                                    <div class="ekit-btn-wraper">
                                        <a href="#" class="elementskit-btn  whitespace--normal" id="">
                                            Learn More <i class="icon icon-right-arrow"></i> </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-2c2a580 e-con-full e-flex e-con e-child"
                        data-id="2c2a580" data-element_type="container">
                        <div class="elementor-element elementor-element-73e9142 e-con-full e-flex e-con e-child"
                            data-id="73e9142" data-element_type="container">
                            <div class="elementor-element elementor-element-bc9c29b elementor-widget elementor-widget-heading"
                                data-id="bc9c29b" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h4 class="elementor-heading-title elementor-size-default">Connect with
                                        us</h4>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-6d0c3bb elementor-align-left elementor-icon-list--layout-traditional elementor-list-item-link-full_width elementor-widget elementor-widget-icon-list"
                                data-id="6d0c3bb" data-element_type="widget" data-widget_type="icon-list.default">
                                <div class="elementor-widget-container">
                                    <ul class="elementor-icon-list-items">
                                        <li class="elementor-icon-list-item">
                                            <a href="#">

                                                <span class="elementor-icon-list-text">
                                                    <font size="5">📧</font> &nbsp;&nbsp;
                                                    <EMAIL>
                                                </span>
                                            </a>
                                        </li>
                                        <li class="elementor-icon-list-item">
                                            <a href="#">

                                                <span class="elementor-icon-list-text">
                                                    <font size="5">💬</font> &nbsp;&nbsp; 718.288.7300
                                                </span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-6785420 e-con-full e-flex e-con e-child"
                            data-id="6785420" data-element_type="container">
                            <div class="elementor-element elementor-element-e923f1d elementor-widget elementor-widget-heading"
                                data-id="e923f1d" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h4 class="elementor-heading-title elementor-size-default">Address</h4>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-7bef88b elementor-align-left elementor-icon-list--layout-traditional elementor-list-item-link-full_width elementor-widget elementor-widget-icon-list"
                                data-id="7bef88b" data-element_type="widget" data-widget_type="icon-list.default">
                                <div class="elementor-widget-container">
                                    <ul class="elementor-icon-list-items">
                                        <li class="elementor-icon-list-item">
                                            <a href="#">

                                                <span class="elementor-icon-list-text">
                                                    <font size="5">🌍</font> &nbsp;&nbsp; 21 NE 10th St,
                                                    Deerfield Beach FL 33441
                                                </span>
                                            </a>
                                        </li>
                                        <li class="elementor-icon-list-item">
                                            <a href="#">

                                                <span class="elementor-icon-list-text">
                                                    <font size="5">🕒</font> &nbsp;&nbsp; Monday → Friday
                                                    9am to 5pm
                                                </span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-9b7cd60 e-con-full e-flex e-con e-child"
                            data-id="9b7cd60" data-element_type="container">
                            <div class="elementor-element elementor-element-e3957e2 elementor-widget elementor-widget-heading"
                                data-id="e3957e2" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h4 class="elementor-heading-title elementor-size-default"> Join our
                                        newsletter</h4>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-60dd47a elementor-button-align-stretch elementor-widget elementor-widget-form"
                                data-id="60dd47a" data-element_type="widget"
                                data-settings="{&quot;step_next_label&quot;:&quot;Next&quot;,&quot;step_previous_label&quot;:&quot;Previous&quot;,&quot;button_width&quot;:&quot;20&quot;,&quot;button_width_mobile&quot;:&quot;20&quot;,&quot;step_type&quot;:&quot;number_text&quot;,&quot;step_icon_shape&quot;:&quot;circle&quot;}"
                                data-widget_type="form.default">
                                <div class="elementor-widget-container">
                                    <form class="elementor-form" method="post" name="New Form">
                                        <input type="hidden" name="post_id" value="1764" />
                                        <input type="hidden" name="form_id" value="60dd47a" />
                                        <input type="hidden" name="referer_title" value="Services" />

                                        <input type="hidden" name="queried_id" value="2433" />

                                        <div class="elementor-form-fields-wrapper elementor-labels-above">
                                            <div
                                                class="elementor-field-type-email elementor-field-group elementor-column elementor-field-group-email elementor-col-80 elementor-sm-80 elementor-field-required">
                                                <input size="1" type="email" name="form_fields[email]"
                                                    id="form-field-email"
                                                    class="elementor-field elementor-size-sm  elementor-field-textual"
                                                    placeholder="Email Address" required="required">
                                            </div>
                                            <div
                                                class="elementor-field-group elementor-column elementor-field-type-submit elementor-col-20 e-form__buttons elementor-sm-20">
                                                <button class="elementor-button elementor-size-sm" type="submit">
                                                    <span class="elementor-button-content-wrapper">
                                                        <span class="elementor-button-icon">
                                                            <i aria-hidden="true" class="icon icon-right-arrow"></i>
                                                            <span class="elementor-screen-only">Submit</span>
                                                        </span>
                                                    </span>
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-b9f61ee e-con-full e-flex e-con e-child"
                            data-id="b9f61ee" data-element_type="container">
                            <div class="elementor-element elementor-element-8737e81 elementor-widget elementor-widget-heading"
                                data-id="8737e81" data-element_type="widget" data-widget_type="heading.default">
                                <div class="elementor-widget-container">
                                    <h4 class="elementor-heading-title elementor-size-default">Follow us
                                    </h4>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-265915a e-grid e-con-full e-con e-child"
                                data-id="265915a" data-element_type="container">
                                <div class="elementor-element elementor-element-5a77c23 elementor-widget elementor-widget-elementskit-social-media"
                                    data-id="5a77c23" data-element_type="widget"
                                    data-widget_type="elementskit-social-media.default">
                                    <div class="elementor-widget-container">
                                        <div class="ekit-wid-con">
                                            <ul class="ekit_social_media">
                                                <li class="elementor-repeater-item-633e4a1">
                                                    <a href="#" aria-label="Facebook" class="">
                                                        Facebook </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-652d726 elementor-widget elementor-widget-elementskit-social-media"
                                    data-id="652d726" data-element_type="widget"
                                    data-widget_type="elementskit-social-media.default">
                                    <div class="elementor-widget-container">
                                        <div class="ekit-wid-con">
                                            <ul class="ekit_social_media">
                                                <li class="elementor-repeater-item-55fe18f">
                                                    <a href="#" aria-label="Twitter" class="">
                                                        Twitter </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-c8b0b41 elementor-widget elementor-widget-elementskit-social-media"
                                    data-id="c8b0b41" data-element_type="widget"
                                    data-widget_type="elementskit-social-media.default">
                                    <div class="elementor-widget-container">
                                        <div class="ekit-wid-con">
                                            <ul class="ekit_social_media">
                                                <li class="elementor-repeater-item-55fe18f">
                                                    <a href="#" aria-label="Instagram" class="">
                                                        Instagram </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-7bde8dc elementor-widget elementor-widget-elementskit-social-media"
                                    data-id="7bde8dc" data-element_type="widget"
                                    data-widget_type="elementskit-social-media.default">
                                    <div class="elementor-widget-container">
                                        <div class="ekit-wid-con">
                                            <ul class="ekit_social_media">
                                                <li class="elementor-repeater-item-55fe18f">
                                                    <a href="#" aria-label="LinkedIn" class="">
                                                        LinkedIn </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-dc0f0af e-con-full e-flex e-con e-child"
                        data-id="dc0f0af" data-element_type="container">
                        <div class="elementor-element elementor-element-49dd403 elementor-widget elementor-widget-theme-site-title elementor-widget-heading"
                            data-id="49dd403" data-element_type="widget" data-widget_type="theme-site-title.default"
                            style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                            <div class="elementor-widget-container" style="flex: 1; min-width: 200px;">
                                <p style="margin: 0; font-size: 14px;">
                                    <a href="privacy-policy.html"
                                        style="margin-right: 1rem; text-decoration: underline;">Privacy Policy</a>
                                    <a href="terms.html" style="text-decoration: underline;">Terms of Service</a>
                                </p>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-49dd403 elementor-widget elementor-widget-theme-site-title elementor-widget-heading"
                            data-id="49dd403" data-element_type="widget" data-widget_type="theme-site-title.default"
                            style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                            <div class="elementor-widget-container"
                                style="flex: 1; min-width: 200px; text-align: right;">
                                <h2 class="elementor-heading-title elementor-size-default"
                                    style="margin: 0; font-size: 14px;">
                                    <a href="index.html">Copyright © 2024 Next Level Growth Partners | AI Lead
                                        Conversion Experts</a>
                                </h2>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>


        <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script>
        <script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/*"},{"not":{"href_matches":["\/wp-*.php","\/wp-admin\/*","\/wp-content\/uploads\/*","\/wp-content\/*","\/wp-content\/plugins\/*","\/wp-content\/themes\/hello-elementor\/*","\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
        <script>
            const lazyloadRunObserver = () => {
                const lazyloadBackgrounds = document.querySelectorAll(`.e-con.e-parent:not(.e-lazyloaded)`);
                const lazyloadBackgroundObserver = new IntersectionObserver((entries) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            let lazyloadBackground = entry.target;
                            if (lazyloadBackground) {
                                lazyloadBackground.classList.add('e-lazyloaded');
                            }
                            lazyloadBackgroundObserver.unobserve(entry.target);
                        }
                    });
                }, { rootMargin: '200px 0px 200px 0px' });
                lazyloadBackgrounds.forEach((lazyloadBackground) => {
                    lazyloadBackgroundObserver.observe(lazyloadBackground);
                });
            };
            const events = [
                'DOMContentLoaded',
                'elementor/lazyload/observe',
            ];
            events.forEach((event) => {
                document.addEventListener(event, lazyloadRunObserver);
            });
        </script>
        <script
            src="https://brandmode.1onestrong.com/wp-content/themes/hello-elementor/assets/js/hello-frontend.min.js?ver=3.3.0"
            id="hello-theme-frontend-js"></script>
        <script
            src="https://brandmode.1onestrong.com/wp-content/plugins/elementor-pro/assets/lib/smartmenus/jquery.smartmenus.min.js?ver=1.2.1"
            id="smartmenus-js"></script>
        <script
            src="https://brandmode.1onestrong.com/wp-content/plugins/elementskit-lite/libs/framework/assets/js/frontend-script.js?ver=3.4.0"
            id="elementskit-framework-js-frontend-js"></script>
        <script id="elementskit-framework-js-frontend-js-after">
            var elementskit = {
                resturl: 'https://brandmode.1onestrong.com/wp-json/elementskit/v1/',
            }


        </script>
        <script
            src="https://brandmode.1onestrong.com/wp-content/plugins/elementskit-lite/widgets/init/assets/js/widget-scripts.js?ver=3.4.0"
            id="ekit-widget-scripts-js"></script>
        <script
            src="https://brandmode.1onestrong.com/wp-content/plugins/elementskit-lite/widgets/init/assets/js/odometer.min.js?ver=3.4.0"
            id="odometer-js"></script>
        <script
            src="https://brandmode.1onestrong.com/wp-content/plugins/elementor-pro/assets/js/webpack-pro.runtime.min.js?ver=3.27.2"
            id="elementor-pro-webpack-runtime-js"></script>
        <script
            src="https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/js/webpack.runtime.min.js?ver=3.27.3"
            id="elementor-webpack-runtime-js"></script>
        <script
            src="https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/js/frontend-modules.min.js?ver=3.27.3"
            id="elementor-frontend-modules-js"></script>
        <script src="https://brandmode.1onestrong.com/wp-includes/js/dist/hooks.min.js?ver=4d63a3d491d11ffd8ac6"
            id="wp-hooks-js"></script>
        <script src="https://brandmode.1onestrong.com/wp-includes/js/dist/i18n.min.js?ver=5e580eb46a90c2b997e6"
            id="wp-i18n-js"></script>
        <script id="wp-i18n-js-after">
            wp.i18n.setLocaleData({ 'text direction\u0004ltr': ['ltr'] });
        </script>
        <script id="elementor-pro-frontend-js-before">
            var ElementorProFrontendConfig = { "nonce": "e21583fac5", "urls": { "assets": "https:\/\/brandmode.1onestrong.com\/wp-content\/plugins\/elementor-pro\/assets\/", "rest": "https:\/\/brandmode.1onestrong.com\/wp-json\/" }, "settings": { "lazy_load_background_images": true }, "popup": { "hasPopUps": false }, "shareButtonsNetworks": { "facebook": { "title": "Facebook", "has_counter": true }, "twitter": { "title": "Twitter" }, "linkedin": { "title": "LinkedIn", "has_counter": true }, "pinterest": { "title": "Pinterest", "has_counter": true }, "reddit": { "title": "Reddit", "has_counter": true }, "vk": { "title": "VK", "has_counter": true }, "odnoklassniki": { "title": "OK", "has_counter": true }, "tumblr": { "title": "Tumblr" }, "digg": { "title": "Digg" }, "skype": { "title": "Skype" }, "stumbleupon": { "title": "StumbleUpon", "has_counter": true }, "mix": { "title": "Mix" }, "telegram": { "title": "Telegram" }, "pocket": { "title": "Pocket", "has_counter": true }, "xing": { "title": "XING", "has_counter": true }, "whatsapp": { "title": "WhatsApp" }, "email": { "title": "Email" }, "print": { "title": "Print" }, "x-twitter": { "title": "X" }, "threads": { "title": "Threads" } }, "facebook_sdk": { "lang": "en_US", "app_id": "" }, "lottie": { "defaultAnimationUrl": "https:\/\/brandmode.1onestrong.com\/wp-content\/plugins\/elementor-pro\/modules\/lottie\/assets\/animations\/default.json" } };
        </script>
        <script
            src="https://brandmode.1onestrong.com/wp-content/plugins/elementor-pro/assets/js/frontend.min.js?ver=3.27.2"
            id="elementor-pro-frontend-js"></script>
        <script src="https://brandmode.1onestrong.com/wp-includes/js/jquery/ui/core.min.js?ver=1.13.3"
            id="jquery-ui-core-js"></script>
        <script id="elementor-frontend-js-before">
            var elementorFrontendConfig = { "environmentMode": { "edit": false, "wpPreview": false, "isScriptDebug": false }, "i18n": { "shareOnFacebook": "Share on Facebook", "shareOnTwitter": "Share on Twitter", "pinIt": "Pin it", "download": "Download", "downloadImage": "Download image", "fullscreen": "Fullscreen", "zoom": "Zoom", "share": "Share", "playVideo": "Play Video", "previous": "Previous", "next": "Next", "close": "Close", "a11yCarouselPrevSlideMessage": "Previous slide", "a11yCarouselNextSlideMessage": "Next slide", "a11yCarouselFirstSlideMessage": "This is the first slide", "a11yCarouselLastSlideMessage": "This is the last slide", "a11yCarouselPaginationBulletMessage": "Go to slide" }, "is_rtl": false, "breakpoints": { "xs": 0, "sm": 480, "md": 768, "lg": 1025, "xl": 1440, "xxl": 1600 }, "responsive": { "breakpoints": { "mobile": { "label": "Mobile Portrait", "value": 767, "default_value": 767, "direction": "max", "is_enabled": true }, "mobile_extra": { "label": "Mobile Landscape", "value": 880, "default_value": 880, "direction": "max", "is_enabled": false }, "tablet": { "label": "Tablet Portrait", "value": 1024, "default_value": 1024, "direction": "max", "is_enabled": true }, "tablet_extra": { "label": "Tablet Landscape", "value": 1200, "default_value": 1200, "direction": "max", "is_enabled": false }, "laptop": { "label": "Laptop", "value": 1366, "default_value": 1366, "direction": "max", "is_enabled": true }, "widescreen": { "label": "Widescreen", "value": 2400, "default_value": 2400, "direction": "min", "is_enabled": false } }, "hasCustomBreakpoints": true }, "version": "3.27.3", "is_static": false, "experimentalFeatures": { "e_font_icon_svg": true, "additional_custom_breakpoints": true, "container": true, "e_swiper_latest": true, "e_onboarding": true, "theme_builder_v2": true, "hello-theme-header-footer": true, "home_screen": true, "landing-pages": true, "nested-elements": true, "editor_v2": true, "link-in-bio": true, "floating-buttons": true }, "urls": { "assets": "https:\/\/brandmode.1onestrong.com\/wp-content\/plugins\/elementor\/assets\/", "uploadUrl": "https:\/\/brandmode.1onestrong.com\/wp-content\/uploads" }, "nonces": { "floatingButtonsClickTracking": "b6aeea86e3" }, "swiperClass": "swiper", "settings": { "page": [], "editorPreferences": [] }, "kit": { "body_background_background": "classic", "active_breakpoints": ["viewport_mobile", "viewport_tablet", "viewport_laptop"], "global_image_lightbox": "yes", "lightbox_enable_counter": "yes", "lightbox_enable_fullscreen": "yes", "lightbox_enable_zoom": "yes", "lightbox_enable_share": "yes", "lightbox_title_src": "title", "lightbox_description_src": "description", "hello_header_logo_type": "logo", "hello_header_menu_layout": "horizontal", "hello_footer_logo_type": "logo" }, "post": { "id": 7, "title": "Brandmode%20%E2%80%93%20Digital%20Marketing%20Agency", "excerpt": "", "featuredImage": false } };
        </script>
        <script src="https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/js/frontend.min.js?ver=3.27.3"
            id="elementor-frontend-js"></script>
        <script
            src="https://brandmode.1onestrong.com/wp-content/plugins/elementor-pro/assets/js/elements-handlers.min.js?ver=3.27.2"
            id="pro-elements-handlers-js"></script>
        <script
            src="https://brandmode.1onestrong.com/wp-content/plugins/elementskit-lite/widgets/init/assets/js/animate-circle.min.js?ver=3.4.0"
            id="animate-circle-js"></script>
        <!-- <script id="elementskit-elementor-js-extra">
        var ekit_config = { "ajaxurl": "https:\/\/brandmode.1onestrong.com\/wp-admin\/admin-ajax.php", "nonce": "9a48b9ce84" };
    </script> -->
        <script
            src="https://brandmode.1onestrong.com/wp-content/plugins/elementskit-lite/widgets/init/assets/js/elementor.js?ver=3.4.0"
            id="elementskit-elementor-js"></script>
        <script>

            // const N8N_WEBHOOK_URL = 'https://someco.app.n8n.cloud/webhook-test/a8205c39-11e6-4be8-a95f-6f9ad5df6a25';
            const N8N_WEBHOOK_URL = 'https://someco.app.n8n.cloud/webhook/a8205c39-11e6-4be8-a95f-6f9ad5df6a25';


            let conversationHistory = [
                {
                    role: "system",
                    content: "You are Bella, a friendly AI assistant helping businesses understand how AI can work for them. Keep responses conversational, helpful, and focused on business benefits. Keep responses under 100 words."
                }
            ];

            function addMessage(content, isUser = false) {
                const messagesContainer = document.getElementById('chatMessages');
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isUser ? 'sent' : 'received'}`;

                if (!isUser) {
                    messageDiv.innerHTML = `
                                    <div class="avatar">G</div>
                                    <div class="message-bubble">${content}</div>
                                `;
                } else {
                    messageDiv.innerHTML = `
                                    <div class="message-bubble">${content}</div>
                                `;
                }

                messagesContainer.appendChild(messageDiv);

                // Smooth scroll to bottom
                setTimeout(() => {
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }, 100);
            }

            function showTypingIndicator() {
                const indicator = document.getElementById('typingIndicator');
                const messagesContainer = document.getElementById('chatMessages');

                indicator.style.display = 'block';
                messagesContainer.appendChild(indicator);

                setTimeout(() => {
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }, 100);
            }

            function hideTypingIndicator() {
                const indicator = document.getElementById('typingIndicator');
                indicator.style.display = 'none';
            }

            function updateSendButton() {
                const input = document.getElementById('messageInput');
                const sendBtn = document.getElementById('sendBtn');

                if (input.value.trim()) {
                    sendBtn.style.opacity = '1';
                    sendBtn.disabled = false;
                } else {
                    sendBtn.style.opacity = '0.5';
                    sendBtn.disabled = true;
                }
            }

            async function getAIResponse(userMessage) {
                try {

                    conversationHistory.push({
                        role: "user",
                        content: userMessage
                    });


                    const payloadToN8n = {
                        messages: conversationHistory

                    };

                    const response = await fetch(N8N_WEBHOOK_URL, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',

                        },
                        body: JSON.stringify(payloadToN8n)
                    });

                    if (!response.ok) {
                        // Try to get more specific error from n8n if possible
                        let errorData;
                        try {
                            errorData = await response.json();
                        } catch (e) {
                            // Ignore if response is not JSON
                        }
                        const errorMessage = errorData?.message || `HTTP error! status: ${response.status}`;
                        throw new Error(errorMessage);
                    }

                    const data = await response.json();

                    const aiResponse = data.aiReply;

                    if (!aiResponse) {
                        console.error('AI response not found in n8n data. Expected a field "aiReply". Data received:', data);
                        return "Sorry, I received an unexpected response from the workflow.";
                    }

                    // Add the AI's response to the conversation history
                    conversationHistory.push({
                        role: "assistant",
                        content: aiResponse
                    });

                    return aiResponse;

                } catch (error) {
                    console.error('Error getting AI response from n8n:', error);

                    return `I'm having trouble connecting to the workflow right now. Error: ${error.message}. Please try again in a moment.`;
                }
            }

            async function sendMessage() {
                const input = document.getElementById('messageInput');
                const message = input.value.trim();

                if (!message) return;

                // Add user message
                addMessage(message, true);
                input.value = '';
                updateSendButton();

                // Show typing indicator
                showTypingIndicator();

                // Get AI response
                const aiResponse = await getAIResponse(message);

                // Hide typing indicator and add AI response
                hideTypingIndicator();
                setTimeout(() => {
                    addMessage(aiResponse);
                }, 800); // Added a small delay for a more natural feel
            }

            function handleKeyPress(event) {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    sendMessage();
                }
            }

            function startDemo() {
                // Simulate booking demo
                addMessage("Thanks for your interest! In a real implementation, this would redirect to your booking system. For now, feel free to chat with me to see how AI assistants work!");
            }

            function tryGrace() {
                document.getElementById('messageInput').focus();
                setTimeout(() => {
                    addMessage("Perfect! You can start chatting with me right here. What would you like to know about AI assistants for your business?");
                }, 500);
            }

            // Fullscreen functionality
            let isFullscreen = false;

            function toggleFullscreen() {
                const container = document.getElementById('mainContainer');
                // const mobileFrame = document.querySelector('.mobile-frame'); // Not directly used for class toggling on mobileFrame
                const fullscreenBtn = document.getElementById('fullscreenBtn');
                const navbar = document.getElementById('navbar');

                isFullscreen = !isFullscreen;

                if (isFullscreen) {
                    // Enter fullscreen mode
                    container.classList.add('fullscreen-mode');
                    fullscreenBtn.innerHTML = '🖥️ Exit Full Screen';
                    navbar.style.display = 'none'; // Hide navbar in fullscreen

                    // Disable body scroll
                    document.body.style.overflow = 'hidden';

                } else {
                    // Exit fullscreen mode
                    container.classList.remove('fullscreen-mode');
                    fullscreenBtn.innerHTML = '📱 Full Screen';

                    // Show navbar on mobile if applicable, otherwise it's controlled by CSS/handleResize
                    if (window.innerWidth <= 768) {
                        navbar.style.display = 'flex';
                    } else {
                        navbar.style.display = 'none'; // Ensure it's hidden on desktop if not handled by handleResize
                    }
                    handleResize(); // Call handleResize to correctly set navbar display based on new state

                    // Re-enable body scroll
                    document.body.style.overflow = 'auto';
                }
            }

            // Handle window resize
            function handleResize() {
                const navbar = document.getElementById('navbar');

                if (isFullscreen) { // If in fullscreen, navbar should generally be hidden
                    navbar.classList.remove('show');
                    navbar.style.display = 'none';
                    return;
                }

                if (window.innerWidth <= 768) {
                    navbar.classList.add('show'); // Uses CSS 'display: flex !important' or similar
                    navbar.style.display = 'flex'; // Explicitly set
                } else {
                    navbar.classList.remove('show');
                    navbar.style.display = 'none'; // Hide on wider screens if not in mobile view
                }
            }

            // Enhanced Process Flow Animation
            function initProcessFlowAnimations() {
                const processSteps = document.querySelectorAll('.process-step');
                const processStepItems = document.querySelectorAll('.process-step-item');
                const processTimeline = document.querySelector('.process-timeline');
                const timelineProgressVertical = document.querySelector('.timeline-progress-vertical');

                // Create timeline progress indicator for original process flow
                if (processTimeline && !document.querySelector('.timeline-progress')) {
                    const progressBar = document.createElement('div');
                    progressBar.className = 'timeline-progress';
                    processTimeline.appendChild(progressBar);
                }

                const observerOptions = {
                    threshold: 0.3,
                    rootMargin: '0px 0px -50px 0px'
                };

                let animatedSteps = 0;
                let animatedStepItems = 0;
                const totalSteps = processSteps.length;
                const totalStepItems = processStepItems.length;

                // Observer for original process steps
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach((entry, index) => {
                        if (entry.isIntersecting && !entry.target.classList.contains('animate')) {
                            setTimeout(() => {
                                entry.target.classList.add('animate');
                                animatedSteps++;

                                // Update timeline progress
                                const progressBar = document.querySelector('.timeline-progress');
                                if (progressBar) {
                                    const progress = (animatedSteps / totalSteps) * 100;
                                    progressBar.style.height = progress + '%';
                                    if (progress >= 100) {
                                        progressBar.classList.add('active');
                                    }
                                }

                                // Add sparkle effect
                                createSparkleEffect(entry.target);
                            }, index * 300);
                        }
                    });
                }, observerOptions);

                // Observer for new process step items
                const stepItemObserver = new IntersectionObserver((entries) => {
                    entries.forEach((entry, index) => {
                        if (entry.isIntersecting && !entry.target.classList.contains('animate')) {
                            setTimeout(() => {
                                entry.target.classList.add('animate');
                                animatedStepItems++;

                                // Update vertical timeline progress
                                if (timelineProgressVertical) {
                                    const progress = (animatedStepItems / totalStepItems) * 100;
                                    timelineProgressVertical.style.height = progress + '%';
                                    if (progress >= 100) {
                                        timelineProgressVertical.classList.add('active');
                                    }
                                }

                                // Add enhanced sparkle effect
                                createEnhancedSparkleEffect(entry.target);
                            }, index * 400); // Staggered animation
                        }
                    });
                }, observerOptions);

                processSteps.forEach(step => {
                    observer.observe(step);
                });

                processStepItems.forEach(stepItem => {
                    stepItemObserver.observe(stepItem);
                });
            }

            // Create sparkle effect for animated elements
            function createSparkleEffect(element) {
                const sparkles = 5;
                for (let i = 0; i < sparkles; i++) {
                    setTimeout(() => {
                        const sparkle = document.createElement('div');
                        sparkle.style.cssText = `
                        position: absolute;
                        width: 4px;
                        height: 4px;
                        background: #4facfe;
                        border-radius: 50%;
                        pointer-events: none;
                        z-index: 1000;
                        animation: sparkle 1s ease-out forwards;
                    `;

                        const rect = element.getBoundingClientRect();
                        sparkle.style.left = (rect.left + Math.random() * rect.width) + 'px';
                        sparkle.style.top = (rect.top + Math.random() * rect.height) + 'px';

                        document.body.appendChild(sparkle);

                        setTimeout(() => sparkle.remove(), 1000);
                    }, i * 100);
                }
            }

            // Enhanced sparkle effect for process step items
            function createEnhancedSparkleEffect(element) {
                const sparkles = 8;
                const colors = ['#4facfe', '#00f2fe', '#667eea', '#764ba2'];

                for (let i = 0; i < sparkles; i++) {
                    setTimeout(() => {
                        const sparkle = document.createElement('div');
                        const color = colors[Math.floor(Math.random() * colors.length)];
                        const size = Math.random() * 6 + 3; // 3-9px

                        sparkle.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        background: ${color};
                        border-radius: 50%;
                        pointer-events: none;
                        z-index: 1000;
                        animation: enhancedSparkle 1.5s ease-out forwards;
                        box-shadow: 0 0 10px ${color};
                    `;

                        const rect = element.getBoundingClientRect();
                        sparkle.style.left = (rect.left + Math.random() * rect.width) + 'px';
                        sparkle.style.top = (rect.top + Math.random() * rect.height) + 'px';

                        document.body.appendChild(sparkle);

                        setTimeout(() => sparkle.remove(), 1500);
                    }, i * 150);
                }
            }

            // Add sparkle animation CSS
            const sparkleCSS = `
            @keyframes sparkle {
                0% { transform: scale(0) rotate(0deg); opacity: 1; }
                50% { transform: scale(1) rotate(180deg); opacity: 1; }
                100% { transform: scale(0) rotate(360deg); opacity: 0; }
            }

            @keyframes enhancedSparkle {
                0% {
                    transform: scale(0) rotate(0deg) translateY(0px);
                    opacity: 1;
                }
                25% {
                    transform: scale(1.2) rotate(90deg) translateY(-10px);
                    opacity: 1;
                }
                50% {
                    transform: scale(1) rotate(180deg) translateY(-20px);
                    opacity: 0.8;
                }
                75% {
                    transform: scale(0.8) rotate(270deg) translateY(-30px);
                    opacity: 0.4;
                }
                100% {
                    transform: scale(0) rotate(360deg) translateY(-40px);
                    opacity: 0;
                }
            }
        `;

            if (!document.querySelector('#sparkle-styles')) {
                const style = document.createElement('style');
                style.id = 'sparkle-styles';
                style.textContent = sparkleCSS;
                document.head.appendChild(style);
            }

            // Initialize the chat interface
            window.onload = function () {
                const input = document.getElementById('messageInput');

                // Add input event listener for send button state
                if (input) {
                    input.addEventListener('input', updateSendButton);
                    // Initialize send button state
                    updateSendButton();
                }

                // Handle initial navbar visibility
                handleResize();

                // Add resize listener
                window.addEventListener('resize', handleResize);

                // Initialize process flow animations
                initProcessFlowAnimations();

                // Add keyboard shortcuts
                document.addEventListener('keydown', function (event) {
                    // F11 or F for fullscreen toggle
                    if (event.key === 'F11' || (event.key === 'f' && (event.ctrlKey || event.metaKey))) { // Added metaKey for MacOS
                        event.preventDefault();
                        toggleFullscreen();
                    }

                    if (event.key === 'Escape' && isFullscreen) {
                        toggleFullscreen();
                    }
                });
            };
        </script>
        <script>
            // Initialize ScrollReveal
            ScrollReveal().reveal('.mobile-timeline-item', {
                delay: 200,
                distance: '50px',
                origin: 'bottom',
                interval: 100
            });

            // GSAP animations for more complex elements
            document.addEventListener('DOMContentLoaded', () => {
                gsap.registerPlugin(ScrollTrigger);

                // Animate timeline connectors
                gsap.from(".timeline-connector::after", {
                    scrollTrigger: {
                        trigger: ".timeline-connector",
                        start: "top bottom",
                        toggleActions: "play none none none"
                    },
                    scaleY: 0,
                    transformOrigin: "top center",
                    duration: 1.5,
                    ease: "power3.out"
                });

                // Message bubble animations
                gsap.to(".message-bubble", {
                    scrollTrigger: {
                        trigger: "#cold-outreach",
                        start: "top 80%"
                    },
                    stagger: 0.2,
                    duration: 0.6,
                    opacity: 1,
                    y: 0,
                    ease: "back.out(1)"
                });

                // Countdown animation
                let countdown = document.querySelector('.countdown');
                if (countdown) {
                    let time = 9.4;
                    let interval = setInterval(() => {
                        time -= 0.1;
                        if (time <= 0) {
                            clearInterval(interval);
                            countdown.textContent = "00:00.0 response time";
                        } else {
                            countdown.textContent = `00:0${Math.floor(time)}.${(time % 1).toFixed(1).slice(2)} response time`;
                        }
                    }, 100);
                }

                // Add hover effects to all demo buttons
                document.querySelectorAll('[id^="experience-demo"]').forEach(button => {
                    button.addEventListener('mouseenter', () => {
                        gsap.to(button, {
                            scale: 1.05,
                            duration: 0.3,
                            ease: "power2.out"
                        });
                    });
                    button.addEventListener('mouseleave', () => {
                        gsap.to(button, {
                            scale: 1,
                            duration: 0.3,
                            ease: "power2.out"
                        });
                    });
                });

                // ROI calculator interaction
                const roiSlider = document.querySelector('input[type="range"]');
                if (roiSlider) {
                    roiSlider.addEventListener('input', () => {
                        const value = parseInt(roiSlider.value);
                        const dealSizeSelect = document.querySelector('select');
                        const dealSize = parseInt(dealSizeSelect.value.replace(/\D/g, '')) || 10000;

                        // Simple ROI calculation - adjust as needed
                        const estimatedValue = Math.round(value * dealSize * 0.25); // Assuming 25% conversion

                        document.querySelector('.text-2xl.font-bold.text-blue-600').textContent =
                            `$${estimatedValue.toLocaleString()}`;
                    });
                }
            });
        </script>

        <script>
            document.addEventListener('DOMContentLoaded', function () {
                // DOM Elements
                const monthlyLeadsInput = document.getElementById('monthlyLeads');
                const conversionRateInput = document.getElementById('conversionRate');
                const dealSizeInput = document.getElementById('dealSize');
                const resetBtn = document.getElementById('resetBtn');

                // Display elements
                const monthlyLeadsValue = document.getElementById('monthlyLeadsValue');
                const conversionRateValue = document.getElementById('conversionRateValue');
                const dealSizeValue = document.getElementById('dealSizeValue');
                const currentRevenue = document.getElementById('currentRevenue');
                const currentDeals = document.getElementById('currentDeals');
                const projectedRevenue = document.getElementById('projectedRevenue');
                const projectedDeals = document.getElementById('projectedDeals');
                const roiPercentage = document.getElementById('roiPercentage');
                const annualRevenue = document.getElementById('annualRevenue');
                const currentProgress = document.getElementById('currentProgress');
                const projectedProgress = document.getElementById('projectedProgress');
                const roiCard = document.getElementById('roiCard');
                const leadsPerDeal = document.getElementById('leadsPerDeal');
                const revenuePerLead = document.getElementById('revenuePerLead');
                const closeRateNeeded = document.getElementById('closeRateNeeded');
                const recommendation = document.getElementById('recommendation');

                // Default values
                const defaultValues = {
                    monthlyLeads: 100,
                    conversionRate: 2.5,
                    dealSize: 5000
                };

                // Initialize calculator
                function initCalculator() {
                    calculateROI();

                    // Event listeners for sliders
                    monthlyLeadsInput.addEventListener('input', function () {
                        monthlyLeadsValue.textContent = this.value;
                        calculateROI();
                    });

                    conversionRateInput.addEventListener('input', function () {
                        conversionRateValue.textContent = this.value + '%';
                        calculateROI();
                    });

                    dealSizeInput.addEventListener('input', function () {
                        const value = parseInt(this.value);
                        dealSizeValue.textContent = '$' + value.toLocaleString();
                        calculateROI();
                    });

                    resetBtn.addEventListener('click', function () {
                        monthlyLeadsInput.value = defaultValues.monthlyLeads;
                        conversionRateInput.value = defaultValues.conversionRate;
                        dealSizeInput.value = defaultValues.dealSize;

                        monthlyLeadsValue.textContent = defaultValues.monthlyLeads;
                        conversionRateValue.textContent = defaultValues.conversionRate + '%';
                        dealSizeValue.textContent = '$' + defaultValues.dealSize.toLocaleString();

                        calculateROI();

                        // GSAP animation for reset
                        gsap.from([monthlyLeadsInput, conversionRateInput, dealSizeInput], {
                            duration: 0.5,
                            scale: 1.05,
                            ease: "power2.out"
                        });
                    });
                }

                // Calculate ROI and update display
                function calculateROI() {
                    const monthlyLeads = parseFloat(monthlyLeadsInput.value);
                    const conversionRate = parseFloat(conversionRateInput.value) / 100;
                    const dealSize = parseFloat(dealSizeInput.value);

                    // Current values
                    const currentDealsCount = 2.5; // Fixed for comparision
                    const currentRev = 12500;      // Fixed for comparision

                    // Calculate projected values
                    const dealsClosed = monthlyLeads * conversionRate;
                    const monthlyRev = dealsClosed * dealSize;
                    const annualRev = (monthlyRev - currentRev) * 12;

                    // Calculate ROI percentage
                    let roi = ((monthlyRev - currentRev) / currentRev) * 100;
                    roi = Math.round(roi * 10) / 10; // Round to 1 decimal

                    // Calculate metrics
                    const leadEfficiency = conversionRate > 0 ? Math.round(1 / conversionRate) : '∞';
                    const revPerLead = monthlyRev / monthlyLeads;
                    const dealsNeededForCurrentRev = currentRev / dealSize;

                    // Update display
                    currentRevenue.textContent = '$' + currentRev.toLocaleString();
                    currentDeals.textContent = currentDealsCount;
                    projectedRevenue.textContent = '$' + monthlyRev.toLocaleString();
                    projectedDeals.textContent = dealsClosed.toFixed(2);
                    roiPercentage.textContent = (roi >= 0 ? '+' : '') + roi + '%';
                    annualRevenue.textContent = '$' + annualRev.toLocaleString();
                    leadsPerDeal.textContent = leadEfficiency;
                    revenuePerLead.textContent = revPerLead.toFixed(0);
                    closeRateNeeded.textContent = dealsNeededForCurrentRev.toFixed(2);

                    // Update progress bars with animation
                    const maxRevenue = 100000; // For progress bar scaling
                    const currentProgressPercent = Math.min((currentRev / maxRevenue) * 100, 100);
                    const projectedProgressPercent = Math.min((monthlyRev / maxRevenue) * 100, 100);

                    // Animate progress bars with GSAP
                    gsap.to(currentProgress, {
                        width: currentProgressPercent + '%',
                        backgroundColor: roi < 0 ? '#ef4444' : '#60a5fa',
                        duration: 0.7,
                        ease: "power2.out"
                    });

                    gsap.to(projectedProgress, {
                        width: projectedProgressPercent + '%',
                        backgroundColor: roi >= 0 ? '#10b981' : '#ef4444',
                        duration: 0.7,
                        ease: "power2.out"
                    });

                    // Add pulse animation to ROI card
                    roiCard.classList.add('pulse-animate');
                    setTimeout(() => {
                        roiCard.classList.remove('pulse-animate');
                    }, 500);

                    // Update recommendation based on metrics
                    updateRecommendation(monthlyLeads, conversionRate, dealSize, monthlyRev);
                }

                function updateRecommendation(leads, rate, dealSize, revenue) {
                    let rec = '';
                    const currentRev = 12500; // Baseline
                    const improvementRatio = revenue / currentRev;

                    if (improvementRatio < 1.2) {
                        // Need significant improvement
                        if (rate < 0.03) { // Under 3%
                            rec = "Your conversion rate is very low. Prioritize optimizing your conversion process, as this will have the greatest impact with your current lead volume.";
                        } else {
                            if (leads < 200) {
                                rec = "Your lead volume is limiting your revenue potential. Consider increasing marketing efforts to generate more leads while maintaining your current conversion rate.";
                            } else {
                                rec = "Consider increasing both lead volume and conversion rate to maximize your revenue. Also evaluate if your average deal size aligns with market expectations.";
                            }
                        }
                    } else if (improvementRatio < 1.5) {
                        // Moderate improvement
                        rec = "You're seeing good results with these metrics. Focus on incremental improvements to conversion rate and lead quality to further increase revenue.";
                    } else {
                        // Significant improvement
                        rec = "Your projections show excellent revenue growth potential! Maintain these metrics while optimizing your sales process to handle the increased volume efficiently.";
                    }

                    recommendation.textContent = rec;
                }

                // Initialize the calculator
                initCalculator();
            });
        </script>
        <!-- <script>
        // Modal functionality
        // const modalOverlay = document.getElementById('modalOverlay');
        // const modalContainer = document.getElementById('modalContainer');
        // const openModalBtn = document.getElementById('openModalBtn');
        // const closeModalBtn = document.getElementById('closeModalBtn');
        
        // function openModal() {
        //     modalOverlay.classList.remove('hidden');
        //     modalOverlay.classList.add('flex');
            
        //     // Small delay to allow display property to change before animation
        //     setTimeout(() => {
        //         modalContainer.classList.remove('hidden');
        //     }, 10);
            
        //     document.body.style.overflow = 'hidden';
        // }
        
        // function closeModal() {
        //     modalContainer.classList.add('hidden');
        //     modalOverlay.classList.add('opacity-0');
            
        //     setTimeout(() => {
        //         modalOverlay.classList.add('hidden');
        //         modalOverlay.classList.remove('opacity-0', 'flex');
        //         document.body.style.overflow = 'auto';
        //     }, 300);
        // }
        
        // openModalBtn.addEventListener('click', openModal);
        // closeModalBtn.addEventListener('click', closeModal);
        
        // // Close modal when clicking outside the form
        // modalOverlay.addEventListener('click', function(e) {
        //     if (e.target === modalOverlay) {
        //         closeModal();
        //     }
        // });
        
        // // Close modal with Escape key
        // document.addEventListener('keydown', function(e) {
        //     if (e.key === 'Escape') {
        //         closeModal();
        //     }
        // });
        
        // // Form submission
        // const form = document.querySelector('form[name="New Form"]');
        // form.addEventListener('submit', function(e) {
        //     e.preventDefault();
        //     // Here you would normally handle form submission
        //     alert('Demo request submitted! We will contact you soon.');
        //     closeModal();
        // });

        // Modal functionality
const modalOverlay = document.getElementById('modalOverlay');
const modalContainer = document.getElementById('modalContainer');
const closeModalBtn = document.getElementById('closeModalBtn');

// Attach open modal event to all buttons with class 'openModalBtn'
const openModalBtns = document.querySelectorAll('.openModalBtn');
openModalBtns.forEach(button => {
    button.addEventListener('click', openModal);
});

function openModal() {
    modalOverlay.classList.remove('hidden');
    modalOverlay.classList.add('flex');

    setTimeout(() => {
        modalContainer.classList.remove('hidden');
    }, 10);

    document.body.style.overflow = 'hidden';
}

function closeModal() {
    modalContainer.classList.add('hidden');
    modalOverlay.classList.add('opacity-0');

    setTimeout(() => {
        modalOverlay.classList.add('hidden');
        modalOverlay.classList.remove('opacity-0', 'flex');
        document.body.style.overflow = 'auto';
    }, 300);
}

closeModalBtn.addEventListener('click', closeModal);

// Close modal when clicking outside the form
modalOverlay.addEventListener('click', function (e) {
    if (e.target === modalOverlay) {
        closeModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function (e) {
    if (e.key === 'Escape') {
        closeModal();
    }
});

// Form submission
// const form = document.querySelector('form[name="New Form"]');
// form.addEventListener('submit', function (e) {
//     e.preventDefault();

//     // Show loading state
//     // const submitBtn = form.querySelector('button[type="submit"]');
//     const submitBtn = document.getElementById('bookDemoBtn');
//     const originalText = submitBtn.innerHTML;
//     submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Submitting...';
//     submitBtn.disabled = true;

//     // Collect form data
//     const formData = new FormData(form);
//     const data = {
//         form_type: 'demo_request',
//         source: 'index_page_popup',
//         timestamp: new Date().toISOString(),
//         first_name: formData.get('form_fields[first_name]'),
//         last_name: formData.get('form_fields[last_name]'),
//         email: formData.get('form_fields[email]'),
//         phone: formData.get('form_fields[phone]'),
//         company: formData.get('form_fields[company]'),
//         service_interest: formData.get('form_fields[service]'),
//         referral_source: formData.get('form_fields[referral]'),
//         message: formData.get('form_fields[message]'),
//         sms_consent: formData.get('form_fields[sms_consent]') === 'yes',
//         terms_consent: formData.get('form_fields[terms]') === 'yes',
//         page_url: window.location.href,
//         user_agent: navigator.userAgent
//     };

//     // Send to n8n webhook
//     const webhookUrl = window.WEBHOOK_CONFIG ?
//         window.WEBHOOK_CONFIG.DEMO_FORM_WEBHOOK :
//         'https://someco.app.n8n.cloud/webhook/contact-form';
//         console.log("webhookUrl", webhookUrl);
//     fetch(webhookUrl, {
//         method: 'POST',
//         headers: {
//             'Content-Type': 'application/json',
//         },
//         body: JSON.stringify(data)
//     })
//     .then(response => {
//         if (!response.ok) {
//             throw new Error('Network response was not ok');
//         }
//         return response.json();
//     })
//     .then(result => {
//         // Success
//         submitBtn.innerHTML = '<i class="fas fa-check mr-2"></i> Success!';
//         setTimeout(() => {
//             alert('Demo request submitted successfully! We will contact you soon.');
//             closeModal();
//             // Reset form
//             form.reset();
//             submitBtn.innerHTML = originalText;
//             submitBtn.disabled = false;
//         }, 1500);
//     })
//     .catch(error => {
//         console.error('Error:', error);
//         submitBtn.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i> Error - Try Again';
//         setTimeout(() => {
//             submitBtn.innerHTML = originalText;
//             submitBtn.disabled = false;
//         }, 3000);
//         alert('There was an error submitting your request. Please try again or contact us directly.');
//     });
// });


document.addEventListener('DOMContentLoaded', function () {
    const form = document.querySelector('form[name="New Form"]');

    if (!form) return;

    const modalOverlay = document.getElementById('modalOverlay');
    const modalContainer = document.getElementById('modalContainer');
    const closeModalBtn = document.getElementById('closeModalBtn');
    const submitBtn = document.getElementById('bookDemoBtn');

    modalContainer.addEventListener('click', e => e.stopPropagation());

    form.addEventListener('submit', function (e) {
        e.preventDefault();

        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Submitting...';
        submitBtn.disabled = true;

        const formData = new FormData(form);
        const data = {
            form_type: 'demo_request',
            source: 'index_page_popup',
            timestamp: new Date().toISOString(),
            first_name: formData.get('form_fields[first_name]'),
            last_name: formData.get('form_fields[last_name]'),
            email: formData.get('form_fields[email]'),
            phone: formData.get('form_fields[phone]'),
            company: formData.get('form_fields[company]'),
            service_interest: formData.get('form_fields[service]'),
            referral_source: formData.get('form_fields[referral]'),
            message: formData.get('form_fields[message]'),
            sms_consent: formData.get('form_fields[sms_consent]') === 'yes',
            terms_consent: formData.get('form_fields[terms]') === 'yes',
            page_url: window.location.href,
            user_agent: navigator.userAgent
        };

        const webhookUrl = window.WEBHOOK_CONFIG?.DEMO_FORM_WEBHOOK || 'https://someco.app.n8n.cloud/webhook/contact-form';

        fetch(webhookUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) throw new Error('Network response was not ok');
            return response.text(); // safer for webhooks
        })
        .then(() => {
            submitBtn.innerHTML = '<i class="fas fa-check mr-2"></i> Success!';
            setTimeout(() => {
                alert('Demo request submitted successfully! We will contact you soon.');
                closeModal();
                form.reset();
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 1500);
        })
        .catch(error => {
            console.error('Error:', error);
            submitBtn.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i> Error - Try Again';
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
            alert('There was an error submitting your request. Please try again or contact us directly.');
        });
    });
});

    </script> -->

        <script>
            // Modal functionality
            const modalOverlay = document.getElementById('modalOverlay');
            const modalContainer = document.getElementById('modalContainer');
            const closeModalBtn = document.getElementById('closeModalBtn');

            // Attach open modal event to all buttons with class 'openModalBtn'
            const openModalBtns = document.querySelectorAll('.openModalBtn');
            openModalBtns.forEach(button => {
                button.addEventListener('click', openModal);
            });

            function openModal() {
                modalOverlay.classList.remove('hidden');
                modalOverlay.classList.add('flex');
                setTimeout(() => {
                    modalContainer.classList.remove('hidden');
                }, 10);
                document.body.style.overflow = 'hidden';
            }

            function closeModal() {
                modalContainer.classList.add('hidden');
                modalOverlay.classList.add('opacity-0');
                setTimeout(() => {
                    modalOverlay.classList.add('hidden');
                    modalOverlay.classList.remove('opacity-0', 'flex');
                    document.body.style.overflow = 'auto';
                }, 300);
            }

            closeModalBtn.addEventListener('click', closeModal);

            // Close modal on overlay click
            modalOverlay.addEventListener('click', function (e) {
                if (e.target === modalOverlay) {
                    closeModal();
                }
            });

            // Close modal on Escape key
            document.addEventListener('keydown', function (e) {
                if (e.key === 'Escape') {
                    closeModal();
                }
            });

            // Modal form submission handler
            document.addEventListener('DOMContentLoaded', function () {
                const modalForm = document.querySelector('#modalContainer form');
                if (modalForm) {
                    modalForm.addEventListener('submit', function (e) {
                        e.preventDefault();
                        handleFormSubmission(this, 'demo_request', 'index_page_popup');
                    });
                }
            });

            // Reusable form submission handler
            function handleFormSubmission(form, formType, source) {
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Submitting...';
                submitBtn.disabled = true;

                const formData = new FormData(form);
                let data = {
                    form_type: formType,
                    source: source,
                    timestamp: new Date().toISOString(),
                    page_url: window.location.href,
                    user_agent: navigator.userAgent
                };

                if (formType === 'demo_request' || formType === 'contact_form') {
                    data = {
                        ...data,
                        first_name: formData.get('form_fields[first_name]'),
                        last_name: formData.get('form_fields[last_name]'),
                        email: formData.get('form_fields[email]'),
                        phone: formData.get('form_fields[phone]'),
                        company: formData.get('form_fields[company]'),
                        service_interest: formData.get('form_fields[service]'),
                        referral_source: formData.get('form_fields[referral]'),
                        message: formData.get('form_fields[message]'),
                        sms_consent: formData.get('form_fields[sms_consent]') === 'yes',
                        terms_consent: formData.get('form_fields[terms]') === 'yes'
                    };
                } else if (formType === 'newsletter_signup') {
                    data.email = formData.get('form_fields[email]');
                }

                const webhookUrl = window.WEBHOOK_CONFIG ?
                    window.WEBHOOK_CONFIG.DEMO_FORM_WEBHOOK :
                    'https://someco.app.n8n.cloud/webhook/contact-form';

                fetch(webhookUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                })
                    .then(response => {
                        if (!response.ok) throw new Error('Network response was not ok');
                        return response.json();
                    })
                    .then(result => {
                        submitBtn.innerHTML = '<i class="fas fa-check mr-2"></i> Success!';
                        setTimeout(() => {
                            // alert(formType === 'demo_request' ? 'Demo request submitted successfully! We will contact you soon.' : 'Success!');
                            closeModal();
                            form.reset();
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                        }, 1500);
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        submitBtn.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i> Error - Try Again';
                        setTimeout(() => {
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                        }, 3000);
                        // alert('There was an error submitting your request. Please try again or contact us directly.');
                    });
            }
        </script>

        <script>
            // Initialize AOS animations
            AOS.init({
                duration: 600,
                easing: 'ease-out',
                once: true,
                offset: 100
            });

            // Chat Widget Functionality
            document.addEventListener('DOMContentLoaded', function () {
                const chatWidgetButton = document.getElementById('chatWidgetButton');
                const chatWidgetPopup = document.getElementById('chatWidgetPopup');
                const chatCloseBtn = document.getElementById('chatCloseBtn');
                const chatNotification = document.getElementById('chatNotification');
                const chatInputField = document.querySelector('.chat-input-field');
                const chatSendBtn = document.querySelector('.chat-send-btn');
                const chatMessages = document.querySelector('.chat-widget-messages');
                const typingIndicator = document.querySelector('.chat-typing-indicator');

                let isOpen = false;

                // Toggle chat widget
                function toggleChat() {
                    isOpen = !isOpen;
                    if (isOpen) {
                        chatWidgetPopup.classList.add('show');
                        chatNotification.style.display = 'none';
                        chatInputField.focus();
                    } else {
                        chatWidgetPopup.classList.remove('show');
                    }
                }

                // Event listeners
                chatWidgetButton.addEventListener('click', toggleChat);
                chatCloseBtn.addEventListener('click', toggleChat);

                // Close chat when clicking outside
                document.addEventListener('click', function (e) {
                    if (isOpen && !chatWidgetPopup.contains(e.target) && !chatWidgetButton.contains(e.target)) {
                        toggleChat();
                    }
                });

                // Enable/disable send button based on input
                chatInputField.addEventListener('input', function () {
                    chatSendBtn.disabled = this.value.trim() === '';
                });

                // Send message on Enter key
                chatInputField.addEventListener('keypress', function (e) {
                    if (e.key === 'Enter' && !chatSendBtn.disabled) {
                        sendChatMessage();
                    }
                });

                // Send message on button click
                chatSendBtn.addEventListener('click', function () {
                    if (!this.disabled) {
                        sendChatMessage();
                    }
                });

                function sendChatMessage() {
                    const message = chatInputField.value.trim();
                    if (!message) return;

                    // Add user message to chat
                    addMessageToChat(message, 'sent');

                    // Clear input
                    chatInputField.value = '';
                    chatSendBtn.disabled = true;

                    // Show typing indicator
                    showTypingIndicator();

                    // Simulate AI response (replace with actual n8n integration)
                    setTimeout(() => {
                        hideTypingIndicator();
                        addMessageToChat("Thanks for your message! I'm processing your request and will respond shortly.", 'received');
                    }, 1500);
                }

                function addMessageToChat(message, type) {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = `chat-message ${type}`;

                    const avatarDiv = document.createElement('div');
                    avatarDiv.className = 'chat-message-avatar';
                    avatarDiv.innerHTML = type === 'sent' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

                    const bubbleDiv = document.createElement('div');
                    bubbleDiv.className = 'chat-message-bubble';
                    bubbleDiv.textContent = message;

                    messageDiv.appendChild(avatarDiv);
                    messageDiv.appendChild(bubbleDiv);

                    chatMessages.appendChild(messageDiv);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }

                function showTypingIndicator() {
                    typingIndicator.classList.add('show');
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }

                function hideTypingIndicator() {
                    typingIndicator.classList.remove('show');
                }

                // Show notification after 3 seconds
                setTimeout(() => {
                    if (!isOpen) {
                        chatNotification.style.display = 'flex';
                    }
                }, 3000);
            });
        </script>



</body>



</html>